#include "rwip_config.h"
#include "arch_api.h"
#include "uart.h"
#include "gpio.h"
#include "ke_msg.h"
#include "ke_task.h"
#include "app.h"
#include "app_task.h"
#include "custs1_task.h"
#include "user_custs1_def.h"
#include "user_periph_setup.h"
#include "ppg_uart_handler.h"
#include <string.h>

// Simple PPG data handling - no communication manager
static uint8_t uart_rx_buffer[32];
static uint8_t rx_index = 0;

// UART interrupt handler
void simple_uart_rx_handler(void)
{
    uint8_t received_byte;
    
    // Read one byte
    uart_read(UART1, &received_byte, 1);
    
    // Simple state machine for packet detection
    static enum {
        WAIT_START1,
        WAIT_START2, 
        COLLECT_DATA,
        PACKET_COMPLETE
    } rx_state = WAIT_START1;
    
    switch(rx_state) {
        case WAIT_START1:
            if(received_byte == 0xAA) {
                uart_rx_buffer[0] = received_byte;
                rx_index = 1;
                rx_state = WAIT_START2;
            }
            break;
            
        case WAIT_START2:
            if(received_byte == 0x55) {
                uart_rx_buffer[1] = received_byte;
                rx_index = 2;
                rx_state = COLLECT_DATA;
            } else {
                rx_state = WAIT_START1;
            }
            break;
            
        case COLLECT_DATA:
            uart_rx_buffer[rx_index++] = received_byte;
            if(rx_index >= 12) {
                rx_state = PACKET_COMPLETE;
            }
            break;
            
        case PACKET_COMPLETE:
            // Verify end marker and checksum
            if(uart_rx_buffer[11] == 0xFF) {
                // Verify checksum
                uint8_t checksum = 0;
                for(int i = 2; i < 10; i++) {
                    checksum ^= uart_rx_buffer[i];
                }
                
                if(checksum == uart_rx_buffer[10]) {
                    // Valid packet - send over BLE immediately
                    send_ppg_over_ble();
                }
            }
            rx_state = WAIT_START1;
            rx_index = 0;
            break;
    }
}

// Simple BLE transmission function
void send_ppg_over_ble(void)
{
    // Check if client is connected
    if (ke_state_get(TASK_APP) != APP_CONNECTED) {
        return;
    }
    
    // Extract RED and IR values from received packet
    uint32_t red_value = uart_rx_buffer[2] | 
                        (uart_rx_buffer[3] << 8) | 
                        (uart_rx_buffer[4] << 16) | 
                        (uart_rx_buffer[5] << 24);
                        
    uint32_t ir_value = uart_rx_buffer[6] | 
                       (uart_rx_buffer[7] << 8) | 
                       (uart_rx_buffer[8] << 16) | 
                       (uart_rx_buffer[9] << 24);
    
    // Create BLE packet (16 bytes)
    uint8_t ble_packet[16];
    
    // PPG data identifier
    ble_packet[0] = PPG_DATA_RED_IR;  // 0x20
    
    // RED value (4 bytes)
    ble_packet[1] = red_value & 0xFF;
    ble_packet[2] = (red_value >> 8) & 0xFF;
    ble_packet[3] = (red_value >> 16) & 0xFF;
    ble_packet[4] = (red_value >> 24) & 0xFF;
    
    // IR value (4 bytes)
    ble_packet[5] = ir_value & 0xFF;
    ble_packet[6] = (ir_value >> 8) & 0xFF;
    ble_packet[7] = (ir_value >> 16) & 0xFF;
    ble_packet[8] = (ir_value >> 24) & 0xFF;
    
    // Timestamp (4 bytes) - use system timer
    uint32_t timestamp = ke_time();
    ble_packet[9] = timestamp & 0xFF;
    ble_packet[10] = (timestamp >> 8) & 0xFF;
    ble_packet[11] = (timestamp >> 16) & 0xFF;
    ble_packet[12] = (timestamp >> 24) & 0xFF;
    
    // Reserved bytes
    ble_packet[13] = 0x00;
    ble_packet[14] = 0x00;
    ble_packet[15] = 0x00;
    
    // Send using CUSTS1 profile
    struct custs1_val_ind_req* req = KE_MSG_ALLOC_DYN(CUSTS1_VAL_IND_REQ,
                                                      prf_get_task_from_id(TASK_ID_CUSTS1),
                                                      TASK_APP,
                                                      custs1_val_ind_req,
                                                      16);
    
    req->conidx = 0; // Connection index
    req->handle = SVC1_IDX_VITAL_VAL_VAL; // VITAL characteristic handle
    req->length = 16;
    memcpy(req->value, ble_packet, 16);
    
    ke_msg_send(req);
}

// UART1 interrupt service routine
void UART_Handler(void)
{
    // Check if data is available
    if(uart_rxdata_intr_getf(UART1)) {
        simple_uart_rx_handler();
    }
}

// Initialize UART for PPG data reception
void init_simple_ppg_uart(void)
{
    // Configure UART1 for PPG data reception
    static uart_cfg_t uart_cfg = {
        .baud_rate = UART_BAUDRATE_230400,
        .data_bits = UART_DATABITS_8,
        .parity = UART_PARITY_NONE,
        .stop_bits = UART_STOPBITS_1,
        .auto_flow_control = UART_AFCE_DIS,
        .use_fifo = UART_FIFO_EN,
        .tx_fifo_tr_lvl = UART_TX_FIFO_LEVEL_0,
        .rx_fifo_tr_lvl = UART_RX_FIFO_LEVEL_0,
        .intr_priority = 2,
    };
    
    // Configure GPIO pins for UART1
    GPIO_ConfigurePin(UART1_TX_PORT, UART1_TX_PIN, OUTPUT, PID_UART1_TX, false);
    GPIO_ConfigurePin(UART1_RX_PORT, UART1_RX_PIN, INPUT, PID_UART1_RX, false);
    GPIO_set_pad_latch_en(true);
    
    // Initialize UART
    uart_initialize(UART1, &uart_cfg);
    
    // Enable RX interrupts
    uart_rxdata_intr_setf(UART1, UART_BIT_EN);
    
    // Register interrupt handler
    NVIC_SetPriority(UART_INTR(UART1), 2);
    NVIC_EnableIRQ(UART_INTR(UART1));
}
