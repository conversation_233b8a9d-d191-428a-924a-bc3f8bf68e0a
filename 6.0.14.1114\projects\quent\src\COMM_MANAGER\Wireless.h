/*
 * Wireless.h
 *
 *  Created on: 11-Apr-2022
 *      Author: admin
 */

#ifndef WIRELESS_H_
#define WIRELESS_H_

#include "Awt_Types.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

# define WIRELESS_HR                      0x01
# define WIRELESS_SPO2                    0x02
# define WIRELESS_CBT                     0x03
# define WIRELESS_BATTERY_PERCENTAGE      0x05
# define WIRELESS_BP                      0x06
# define WIRELESS_STEP_CNT                0x07
# define WIRELESS_TIME_SYNC               0X09
# define WIRELESS_STEP_GOAL               0x08
# define BPALGO_HR                        0x17

# define ACKNOWLEDGEMENT                  0x09
# define WATCH_STATUS                     0x0A
# define STEP_COUNT_RESET                 0x0B
# define FALL_DETECTED                    0x0D
# define MALFUNCTION_ERROR                0x0E
# define VITAL_OUTOF_RANGE                0x0F
# define VITAL_UNSERVICEABLE_FLAG         0x10
# define TIME_SYNC_REQUEST                0x11
# define CONNECTIVITY_HEARTBEAT           0x12
# define CHECK_UPDATES                    0x13
# define OTA_MODE_UPADATE                 0x14
# define FACTORY_RESET                    0x15
# define CONFIG_DOWNLOAD_FLAG             0x16
# define UPDATE_HANDLER_OTA               0x0C
# define REMOTE_SOSUI                     0x22

# define BOND_DATA_LEN			644
# define SOFTWARE_VER_OFFSET    30

//# define WIRELESS_SCH_TIME                0x0B
# define WIRELESS_ACK                     0x0B
#define  WIRELESS_ECG					  0xAA

#define WIRELESS_SOS 					  0x0A

/*************UNSERVICEABLE VITAL DEF***************/
# define HR_UNSERVICEABLE                 0x01
# define SPO2_UNSERVICEABLE               0x02
# define BP_UNSERVICEABLE				  0x06
# define CBT_UNSERVICEABLE				  0x03
# define ECG_UNSERVICEABLE         		  0xAA
/**************UNSERVICEABLE DATA******************/

# define USER_IN_MOTION                   0x00
# define WATCH_IS_NOT_WORN                0x01
# define MEASUREMENT_FAILED               0x02
# define SENSOR_FAILED                    0x03
# define BP_FINGER_NOT_PLACED_TIMOUT      0x04
# define EKG_FINGER_NOT_PLACED_TIMOUT     0x05
# define CBT_VAL_NOTAVAIL_STILLCALIBRAT   0x06
# define READING_IN_PROGERESS             0x07
# define SCHEDULER_IS_TAKING_READINGS     0x08
# define REMOTE_BP_REQ_DECLINE            0x09
# define REMOTE_ECG_REQ_DECLINE           0x0A

# define LOWER_RANGE        0x00
# define UPPER_RANGE        0xFA        // 250
# define ECG_PKT_SIZE		150
# define ECG_HEADER_SIZE	0x05
# define MAX_ECG_SAMPLES	80

# define ERR_VITAL_DECLINE	    254
# define ERR_VITAL_BUSY			    255
# define ERR_VITAL_LBATT				253



/************************END**************************/
typedef enum vitals_request_origin
{
	Watch = 0x00,
	sch	  = 0x01,
	APP	  = 0x02,

}vitals_request_origin_t; // As per BLE spec..

enum comm_subid
{
	// Id's from 0x0000 to 0x0028 are reserved for VITAL_PARAMETER_ID
	comm_NONE                  = 0x0028,
	comm_wHR                   = 0x0029,
	comm_wSPO2                 = 0x002A,
	comm_wCBT                  = 0x002B,
	comm_wskintemp             = 0x002C,
	comm_wBP                   = 0x002D,
	comm_wAlgoHr               = 0x002E,
	comm_wECG                  = 0x002F,
	comm_wECGSamples		   = 0x0030,
	comm_wEcgrawData           = 0x0031,
	comm_wSpo2rawData         = 0x0032,
	comm_wPPG_CONTINUOUS       = 0x0033,  // PPG continuous data
	// enum from 8-20 are reserved for raw data

	// CONNECTION PARAMETERS START
	comm_wbleconn              = 0x003D,
	comm_wblediscon            = 0x003E,
	comm_wblepasskey           = 0x003F,
	comm_wblebondinfo          = 0x0040,
	comm_wblemacid             = 0x0041,
	comm_wSwitchtoWifi         = 0x0042,
	comm_wwificonn			   = 0x0043, //Added for wifi
	comm_wwifidiscon		   = 0x0044,
	comm_wwififail			   = 0x0045,
	comm_wwifiscan   		   = 0x0046,
	comm_wwifiApconn		   = 0x0047,
	comm_wwifisavessid		   = 0x0048,
	comm_wradioswitch		   = 0x0049,
	comm_wwificonnparam		   = 0x004A,
	comm_WWifiIpAddre		   = 0x0050,
	// enum from 25-40 is related for connection parameters.

	// CRITICAL PARAMETERS START
	comm_wsos                  = 0x0051,
	comm_wfall                 = 0x0052,

	// OTHER SYSTEM PARAMETERS START
	comm_wbatt                 = 0x0053,
	comm_wstepcount            = 0x0054,
	comm_wStepgoal             = 0x0055,
	comm_wtimesync             = 0x0056,
	comm_wUpdate               = 0x0057,
	comm_wValidBuild		   = 0x0058,
	comm_wOtaReset			   = 0x0059,
	comm_wVitalBusy            = 0x005A,
	comm_wScheduler			   = 0x005B,
	comm_wFactory_Rst          = 0x005C,
	comm_wStepnctRst           = 0x005D,
	comm_wUcConigwrite         = 0x005E,
	comm_wSetupWrComplete      = 0x005F,
	comm_wStepgoalSet          = 0x0060,
	comm_wSchedlrControl       = 0x0061,
	comm_wSchedulerTimeChange  = 0x0062,
	comm_wWatchStatus     	   = 0x0063,
	comm_wVitalAlertRange      = 0x0064,

	// CONFIG PARAMETERS START
	comm_wAlerts               = 0x006F,
	// USER CoNFIG PARAMETERS START
	comm_wUconfig              = 0x0070,
	comm_wUconfigRead          = 0x0071,
	comm_wUconfigack	         = 0x0072,
	comm_wtest                 = 0x0073,
	comm_wClearSettings        = 0x0074,
	comm_wManufacturerName	   = 0x0075,
	comm_wSerialNumber		     = 0x0076,
	comm_wHardwareRevision     = 0x0077,
	comm_wSoftwareRevision     = 0x0078,
	comm_wwifiboot			       = 0x0079,
	comm_wUcConfigDwnld        = 0x0080,
	comm_err
	// Id's From 0x007C to 0xFFFF are unused.
};



enum vital_busy_reasons{
	iUser_in_motion          = 0x00,
	iWatch_not_worn          = 0x01,
	iMeasurement_failed      = 0x02,
	iSensor_failed           = 0x03,
	iBp_finger_notplaced     = 0x04,
	iEkg_finger_notplaced    = 0x05,
	iCbt_calibration_fail    = 0x06,
	iReading_inprogress      = 0x07,
	iScheduler_running    	 = 0x08,
	iRemote_bpreq_declined   = 0x09,
	iRemote_Ekgreq_declined  = 0x0A,

};


enum Signal {D_Low,D_High};

void COM_RXEvent	(void);
void COM_TXCEvent	(void);
void COM_BLEWakeup 	(enum Signal signal);
void COM_WiFiWakeup	(enum Signal signal);
void COM_STWakeup (enum Signal signal);
void COM_BreakEvent (void);

typedef void (*Event_Callback)(uint32_t status);

void Command_Status(uint32_t status);
void Response_Status(uint32_t status);
void Indication_Status(uint32_t status);
void Bufferxfer_Status(uint32_t status);


int32_t Com_BufferXfer(enum comm_subid subid,uint8_t* data, uint16_t len);
int32_t Com_Send_Data(enum comm_subid subid,uint8_t* data, uint16_t len);
int32_t Com_Get_Data(enum comm_subid subid);
int isPrimitiveIdle(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /* WIRELESS_H_ */
