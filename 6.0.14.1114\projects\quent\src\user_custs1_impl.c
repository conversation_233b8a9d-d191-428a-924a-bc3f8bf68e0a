/**
 ****************************************************************************************
 *
 * @file user_custs1_impl.c
 *
 * @brief Peripheral project Custom1 Server implementation source code.
 *
 * Copyright (C) 2015-2019 Dialog Semiconductor.
 * This computer program includes Confidential, Proprietary Information
 * of Dialog Semiconductor. All Rights Reserved.
 *
 ****************************************************************************************
 */

/*
 * INCLUDE FILES
 ****************************************************************************************
 */

#include "gpio.h"
#include "app_api.h"
#include "app.h"
#include "prf_utils.h"
#include "custs1.h"
#include "custs1_task.h"
#include "user_custs1_def.h"
#include "user_custs1_impl.h"
#include "user_peripheral.h"
#include "user_periph_setup.h"
#include "User_Application.h"
#include "UpperLayerInterface.h"
#define _USE_32BIT_TIME_T
#include "time.h"

#define SCHEDULER_TIMESET   0x17

# define UNSERVICEABLE_FLAG   0x10
# define READING_IN_PROGRESS  0x07
# define SCHEDULER_RUNNING    0x08
# define SCHDILER_ORIGIN      0x01
# define EXTERNAL_ENTITY      0x02
# define ALERTS_LENGTH        129
# define MAX_SUBSCRIPTIONS			0x05
# define INDICATION_SUBSCRIBED	0x02

__attribute__((section("text")))

char device_config[700] = "Quent_Boot"; //don't change sub string name

bool Comm_Lock = false;
uint16_t Indication_Src_ID = 0x00;
alert_notification_t alert_ntfy_data;
void USER_INFO_SENDING_Function(struct custs1_val_write_ind const *ptr); 
void Stop_Scheduler(void);
extern void start_timer(void);

static uint8_t conn_status_flag = 0;

/*
 * ENUMS
 ****************************************************************************************
 */


/*
 * GLOBAL VARIABLE DEFINITIONS
 ****************************************************************************************
 */
 
 #define DEBUG_ON 0
ke_msg_id_t timer_used; 

extern uint8_t Scheduler_running;



 /* FUNCTION DEFINITIONS
 ****************************************************************************************
 */

void Update_Ind_srcid(uint16_t id)
{
	Indication_Src_ID = id;
}
	



void Get_Indication_src_id(uint16_t *ID)
{
		*ID = Indication_Src_ID;
}

uint8_t * Get_Software_Revision(void)
{
		return (uint8_t *)(&device_config[SOFT_REVISION_OFFSET]);
	
}



#if BLE_DEUBBBGING_ON
void Send_Data(uint8_t *Param)
{

	
	if (ke_state_get(TASK_APP) == APP_CONNECTED)
	{
		struct custs1_val_ntf_ind_req *req = KE_MSG_ALLOC_DYN(CUSTS1_VAL_NTF_REQ,
                                                          prf_get_task_from_id(TASK_ID_CUSTS1),
                                                          TASK_APP,
                                                          custs1_val_ntf_ind_req,
                                                         8);
		

    req->handle = VITAL_VAL;
    req->length = 8;
    req->notification = true;
    memcpy(req->value, Param, 8);

    ke_msg_send(req);
		
		if(Param[0] != ACK_TO_APP)
		{
			ResetCommLock();
		}
		
	 // TODO, Implement the same in alerts service val.
	}
}

#endif





void Send_NTF_User_Info_Data(uint8_t *usr_info)
{
	
if (ke_state_get(TASK_APP) == APP_CONNECTED)
{
	
struct custs1_val_ntf_ind_req *req = KE_MSG_ALLOC_DYN(CUSTS1_VAL_NTF_REQ,
                                                          prf_get_task_from_id(TASK_ID_CUSTS1),
                                                          TASK_APP,
                                                          custs1_val_ntf_ind_req,
                                                         USER_INFO_SERVICE_NAME_LEN);
		

    
    req->handle = USR_NAME_1_VAL;
    req->length = USER_INFO_SERVICE_NAME_LEN;
    req->notification = true;
    memcpy(req->value, usr_info, USER_INFO_SERVICE_NAME_LEN);
    ke_msg_send(req);
}


}


int wr_counter;
void ECG_SAMPLES_Write_Handler(uint8_t *Ecg_Request)
{

	if(*Ecg_Request == EKG)
	{
		notify_stm(COMMAND, comm_wECG, NULL, NULL);
	}
	
}

void PPG_SAMPLES_Write_Handler(uint8_t *PPg_Request)
{

	// notify_stm(PPG_SAMPLES_ID, *PPg_Request, NULL, NULL);
	
}

extern uint8_t BLE_MAC_ID[6]; //for PUT added

//Function to send the User Info Data
void USER_INFO_SENDING_Function(struct custs1_val_write_ind const *ptr)
{
	//uint8_t *user_info_field = (uint8_t *)ptr->value;
	//uint8_t len = user_info_field[1];	
	DELAY_500_US();
	
	// Byte 0 to Byte 12 is the user info header.
	// Payload is of 128 bytes when the mode is BULK write and BULK read.
	switch(ptr->value[0])
	{
		case 1:
			notify_stm(INDICATION, comm_wUconfig, (uint8_t *)ptr->value, ptr->length);	// BULK WRITE Scenario
			break;
		case 2:
			notify_stm(INDICATION, comm_wUconfig, (uint8_t *)ptr->value, ptr->length);		// BULK read Scenario
			break;
		case 3:
			break;
		case 4:
			break;
		
	}	
	return;
}




//Function to send alert services added rohith
void update_alert_data(struct custs1_val_write_ind const *ptr)
{
	uint8_t *alert_info = (uint8_t *)&ptr->value[1];
	uint8_t size = ptr->value[0];
	
	if(size > ALERTS_LENGTH)
	{
		uint8_t error[8] = {0xff}; //not valid alert msg
		Send_To_Gatt_Client(error, VITAL_CHAR_LEN, VITAL_VAL); 
	}
	else
	{
		notify_stm(INDICATION, comm_wAlerts, alert_info, size);
	}	
}

uint8_t notify_stm(uint8_t id, uint16_t Sub_Id, uint8_t *Data, uint16_t Len)
{
	static uint8_t payloadPlaceHolder[160] __SECTION_ZERO("free_area");
	memset(payloadPlaceHolder, 0, sizeof(payloadPlaceHolder));
	
	if(Data!=NULL)
	{
		memcpy(payloadPlaceHolder, Data, Len);
	}
	
  if(id == INDICATION)
  {
	  return Com_Send_Data(Sub_Id,payloadPlaceHolder,Len);
	}
	else if(id == COMMAND)
	{
		return Com_Get_Data(Sub_Id);
	}
	else
	{
	}
	
	return -1;
}

/*****************************************************************************
* Function used to Send ECG Samples to the Mobile App												 *
******************************************************************************/
void Send_ECG(uint8_t *Ecg_data)
{

	
	  struct custs1_val_ntf_ind_req *req = KE_MSG_ALLOC_DYN(CUSTS1_VAL_NTF_REQ,
                                                          prf_get_task_from_id(TASK_ID_CUSTS1),
                                                          TASK_APP,
                                                          custs1_val_ntf_ind_req,
                                                          ECG_SAMPLES_CHAR_LEN);		
    req->handle = SVC1_ECG_1_VAL;
    req->length = ECG_SAMPLES_CHAR_LEN;
    req->notification = true;
    memcpy(req->value, Ecg_data, ECG_SAMPLES_CHAR_LEN);

    ke_msg_send(req);
	
		memset(Ecg_data,0,ECG_SAMPLES_CHAR_LEN);


}

/*****************************************************************************
* Function used to Send PPG Samples to the Mobile App												 *
******************************************************************************/
void Send_PPG(uint8_t *ppg_data)
{

	
if (ke_state_get(TASK_APP) == APP_CONNECTED)
{
	  struct custs1_val_ntf_ind_req *req = KE_MSG_ALLOC_DYN(CUSTS1_VAL_NTF_REQ,
                                                          prf_get_task_from_id(TASK_ID_CUSTS1),
                                                          TASK_APP,
                                                          custs1_val_ntf_ind_req,
                                                          PPG_SAMPLES_CHAR_LEN);
		//for(uint32_t i =0; i < 10000; i++);
		
    req->handle = SVC1_PPG_1_VAL;
    req->length = PPG_SAMPLES_CHAR_LEN;
    req->notification = true;
    memcpy(req->value, ppg_data, PPG_SAMPLES_CHAR_LEN);

    ke_msg_send(req);
	
		memset(ppg_data,0,PPG_SAMPLES_CHAR_LEN);

}
		
		
}


void UpdateVitalsReq(uint8_t *Vital_Data)
{
	
	uint8_t Return = 0x00;
	
	switch(*Vital_Data)
	{
		
		case HEART_RATE:
			Return = notify_stm(COMMAND, comm_wHR, NULL, NULL);
		break;
		
		case BLOOD_OXYGEN:		
				Return = notify_stm(COMMAND, comm_wSPO2, NULL, NULL);
		break;
		case BODY_TEMPERATURE:
				Return = notify_stm(COMMAND, comm_wCBT, NULL, NULL);
		break;
		case BLOOD_PRESSURE:
			Return = notify_stm(COMMAND, comm_wBP, NULL, NULL); // disabled for march 30 release.
			break;
		
		case BATTERY_PERCENTAGE:
			Return = notify_stm(COMMAND, comm_wbatt, NULL, NULL);
			break;
		
		case TIME_SYNC:
			// endianess convertion big to small	
	   	Return        = Vital_Data[3];    
		  Vital_Data[3] = Vital_Data[6];		
	  	Vital_Data[6] = Return;		        
	  	Return        = Vital_Data[4];	    
	  	Vital_Data[4] = Vital_Data[5];		
	  	Vital_Data[5] = Return;	
		
			Return = notify_stm(INDICATION, comm_wtimesync, &Vital_Data[3], sizeof(uint32_t));
		
		  	break;

		case PEDOMETER_SENSOR:
				Return = notify_stm(COMMAND, comm_wstepcount, NULL, NULL); 		
			break;
		
		case STEP_GOAL:
			Return = Vital_Data[1];
			Vital_Data[1] = Vital_Data[2];
			Vital_Data[2] = Return;
			Return = notify_stm(INDICATION, comm_wStepgoalSet, &Vital_Data[1], sizeof(uint16_t));
			break;	

		case UPDATE_HANDLER_OTA:
				Return = notify_stm(INDICATION, comm_wValidBuild, &Vital_Data[2], sizeof(uint8_t));
			break;
		
		case SOS:
			Return = notify_stm(INDICATION, comm_wsos, &Vital_Data[2], sizeof(uint8_t) );
		break;
		
		case SETUP_CONFIG:
			Return = notify_stm(INDICATION, comm_wSetupWrComplete, NULL, NULL);
		break;
		
		case BLE_MACID:
			Return = notify_stm(COMMAND, comm_wblemacid, NULL, NULL);
			break;		
		default:
		break;
		
		
	}
	
	if(Return)
	{
		// Notify App 
	}
	
	
}


void ResetCommLock_cb(void)
{
	if(Comm_Lock)
	{
		ResetCommLock() ;
		// Notify App
	}
	
}

void StartMeasuremet_Wait_Timer(void)
{
		
	app_easy_timer(40 * 100, ResetCommLock_cb);

}

// @END

void ResetCommLock(void)
{
	
	Comm_Lock = false ; 
#ifdef AWT_SCHEDULER	
	Scheduler_running = false; //vital unseviceable scheduler running
#endif
	
}





uint32_t request_counter =0;



uint8_t* GetBondInfo(void)
{	
	return (uint8_t *)(&device_config[BND_INFO_OFSET]);
}


/* Indication Related Changes */

enum exec_req Send_To_Gatt_Client(uint8_t *Param, uint16_t Service_Len, uint8_t Handle_Value)
{
	uint16_t src_id;
	if (ke_state_get(TASK_APP) == APP_CONNECTED)
	{
		struct custs1_val_ind_req* req = KE_MSG_ALLOC_DYN(CUSTS1_VAL_IND_REQ,
                                                          prf_get_task_from_id(TASK_ID_CUSTS1),
                                                          TASK_APP,
                                                          custs1_val_ind_req,
                                                          Service_Len);
		//get src ID
		Get_Indication_src_id(&src_id);
		uint8_t conidx = KE_IDX_GET(src_id);
		
    req->conidx = app_env[conidx].conidx;
		req->handle = Handle_Value;
    req->length = Service_Len;
		memcpy(req->value, Param, Service_Len);

    ke_msg_send(req);
		
		if(Param[0] != ACK_TO_APP){
			ResetCommLock();
		}
		
		
		return UL_C;
	}else{
		return UL_F;
	}
}

void handle_Ind_Cfm( struct custs1_val_ind_cfm const *msg_param)
{	
		
	/*if(msg_param->handle == SVC1_PPG_1_VAL && msg_param->status == IND_SUCESS){
		//send_Ack(ACK);// send ack/nack
	}
	if(msg_param->handle == SVC1_ECG_1_VAL && msg_param->status == IND_SUCESS){
		//send_Ack(ACK);// send ack/nack
	}
	if(msg_param->handle == VITAL_VAL && msg_param->status == IND_SUCESS){
		if(GPIO_GetPinStatus(GPIO_PORT_0, GPIO_PIN_4))
		{
			// code enters here only if the data was received as an indication.
			//send_Ack(ACK);
		}
	}
	if(msg_param->handle == ALRT_TTL_VAL && msg_param->status == IND_SUCESS){
		//send_Ack(ACK);// send ack/nack
	}
	if(msg_param->handle == USR_NAME_1_VAL && msg_param->status == IND_SUCESS){
	//	send_Ack(ACK);// send ack/nack
	}
	else if(msg_param->handle == SVC1_PPG_1_VAL && msg_param->status!=IND_SUCESS){
		// Send Nack
	}*/
	
}
	



/*
void snd_data_to_stm(OPacket *SendReq)
{
	uart_send(UART1, (uint8_t *)SendReq, SendReq->size + HDR_SIZE, UART_OP_INTR);
}

void serviceCommand(IPacket *Data_Request)
{

}*/



/* Indication Related Changes */

/**************************************************************************************************************************
	*	update_conn_flag
	*	ind_char		 : Id of the characteristic on which an indication has been subscribed/unsubscribed
	*	sub_unsub_val: Whether indication is subscribed/unsubscribed.
	*   0:NOTIFICATION/INDICATION unsubscribed
	*	1: notification subscribed,
	*	2: INDICATION subscribed
	*
	*	MODIFIED: Now enables direct connection after first service subscription instead of waiting for all services
  **************************************************************************************************************************/
void update_conn_flag(uint8_t ind_char, uint8_t sub_unsub_val)
{
	if(sub_unsub_val == INDICATION_SUBSCRIBED)
	{
		conn_status_flag++;

		// Enable immediate connection after first service subscription
		// This allows direct connection without waiting for all MAX_SUBSCRIPTIONS
		if(conn_status_flag == 1)
		{
			// Notify STM that connection is ready immediately after first subscription
			notify_stm(INDICATION, comm_wbleconn, NULL, NULL);
		}
	}

	// Optional: Still track when all services are subscribed for compatibility
	if(conn_status_flag == MAX_SUBSCRIPTIONS)
	{
		// All services subscribed - connection fully established
		// This can be used for additional functionality if needed
		conn_status_flag = 0x00; // Reset counter for next connection cycle
	}
}

void clear_conn_status(void)
{
		conn_status_flag = 0;
}

#ifdef DEBUG_ENABLE
//NODO: for debug
extern uint8_t rxDebugCount;
extern uint8_t breakCount;
extern uint8_t GetTransportState(void);
extern void GetPrimState(uint8_t *istate, uint8_t* cr_state);
void debug_statemachine(void)
{
		uint8_t states[6];
		states[0] = GetTransportState();	
		GetPrimState(states+1, states+2);
	
		states[3] = GPIO_GetPinStatus(GPIO_PORT_0, GPIO_PIN_4);
		states[4] = rxDebugCount;
		states[5] = breakCount;
	
		Send_To_Gatt_Client(states, 6, WIRELESSS_DBG_1_VAL);
	
}
#endif