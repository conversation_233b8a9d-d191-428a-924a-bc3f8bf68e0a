Component: ARM Compiler 5.06 update 1 (build 61) Tool: armlink [4d35a8]

==============================================================================

Section Cross References

    system_da14531.o(.text) refers to system_da14531.o(.data) for .data
    system_da14531.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    startup_da14531.o(RESET) refers to startup_da14531.o(STACK) for __initial_sp
    startup_da14531.o(RESET) refers to startup_da14531.o(.text) for Reset_Handler
    startup_da14531.o(RESET) refers to rwble.o(.text) for BLE_WAKEUP_LP_Handler
    startup_da14531.o(RESET) refers to uart.o(.text) for UART2_Handler
    startup_da14531.o(RESET) refers to adc_531.o(.text) for ADC_Handler
    startup_da14531.o(RESET) refers to gpio.o(.text) for GPIO0_Handler
    startup_da14531.o(.text) refers to system_da14531.o(.text) for SystemInit
    startup_da14531.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    startup_da14531.o(.text) refers to nmi_handler.o(.text) for NMI_HandlerC
    startup_da14531.o(.text) refers to hardfault_handler.o(.text) for HardFault_HandlerC
    nvds.o(.constdata) refers to nvds.o(.constdata) for nvds_data_storage
    nvds.o(.constdata) refers to arch_system.o(retention_mem_area0) for dev_bdaddr
    arch_main.o(.text) refers to arch_system.o(.text) for system_init
    arch_main.o(.text) refers to serialinterface.o(.text) for EnableRFSwitch
    arch_main.o(.text) refers to rwip.o(.text) for rwip_sleep
    arch_main.o(.text) refers to arch_sleep.o(.text) for arch_get_sleep_mode
    arch_main.o(.text) refers to user_periph_setup.o(.text) for periph_init
    arch_main.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_init
    arch_main.o(.text) refers (Weak) to hw_otpc_531.o(.text) for hw_otpc_clear_dcdc_reserved
    arch_main.o(.text) refers to syscntl.o(.text) for syscntl_dcdc_set_level
    arch_main.o(.text) refers to rwble.o(retention_mem_area0) for arch_rwble_last_event
    arch_main.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    arch_main.o(.text) refers to arch_main.o(retention_mem_area0) for retention_mem_area0
    arch_main.o(.constdata) refers to user_peripheral.o(.text) for user_app_init
    jump_table.o(.constdata) refers to ble_arp.o(.text) for rf_init_func
    jump_table.o(.constdata) refers to uart.o(.text) for UART_Handler_SDK_func
    jump_table.o(.constdata) refers to jump_table.o(.text) for platform_reset_func
    jump_table.o(.constdata) refers to rwble.o(.text) for lld_sleep_compensate_func
    jump_table.o(.constdata) refers to arch_system.o(.text) for lld_sleep_init_func
    jump_table.o(.constdata) refers to patch.o(.text) for JT_lld_test_mode_rx_func
    jump_table.o(.constdata) refers to prf.o(.text) for prf_init_func
    jump_table.o(.constdata) refers to jump_table.o(heap_mem_area_not_ret) for rwip_heap_non_ret
    jump_table.o(.constdata) refers to jump_table.o(heap_env_area) for rwip_heap_env_ret
    jump_table.o(.constdata) refers to jump_table.o(heap_db_area) for rwip_heap_db_ret
    jump_table.o(.constdata) refers to jump_table.o(heap_msg_area) for rwip_heap_msg_ret
    jump_table.o(.constdata) refers to nvds.o(.constdata) for rom_nvds_cfg
    jump_table.o(.constdata) refers to custs1.o(.constdata) for rom_cust_prf_cfg
    jump_table.o(.constdata) refers to prf.o(.constdata) for rom_prf_cfg
    jump_table.o(.constdata) refers to app_entry_point.o(.constdata) for rom_app_task_cfg
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to arch_sleep.o(.text) for arch_set_extended_sleep
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to arch_sleep.o(.text) for arch_get_sleep_mode
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to arch_sleep.o(retention_mem_area0) for retention_mem_area0
    arch_sleep.o(.text) refers to rwble.o(retention_mem_area0) for arch_rwble_last_event
    arch_system.o(.text) refers to arch_system.o(i.__ARM_common_ll_muluu) for __ARM_common_ll_muluu
    arch_system.o(.text) refers to otp_cs.o(.text) for otp_cs_get_xtal_wait_trim
    arch_system.o(.text) refers to adc_531.o(.text) for adc_init
    arch_system.o(.text) refers to ble_arp.o(.text) for rf_recalibration
    arch_system.o(.text) refers to gpio.o(.text) for GPIO_init
    arch_system.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_init
    arch_system.o(.text) refers to otp_hdr.o(.text) for otp_hdr_get_bd_address
    arch_system.o(.text) refers to arch_system.o(retention_mem_area0) for retention_mem_area0
    arch_system.o(.text) refers to arch_system.o(.bss) for .bss
    arch_system.o(.text) refers to arch_system.o(.constdata) for .constdata
    arch_system.o(.text) refers to arch_rom.o(.text) for arch_rom_init
    arch_system.o(.text) refers to user_periph_setup.o(.text) for periph_init
    arch_system.o(.text) refers to trng.o(.text) for init_rand_seed_from_trng
    arch_system.o(.text) refers to arch_sleep.o(.text) for arch_disable_sleep
    arch_system.o(.text) refers to app.o(.text) for app_init
    arch_system.o(.text) refers to comm_task.o(.text) for comm_init
    arch_system.o(.text) refers to user_peripheral.o(.text) for user_app_init
    arch_system.o(.text) refers to arch_system.o(retention_mem_area0) for retention_mem_area0
    arch_system.o(.text) refers to arch_system.o(.data) for .data
    arch_system.o(.text) refers to arch_system.o(retention_mem_area0) for retention_mem_area0
    arch_system.o(.constdata) refers to user_peripheral.o(.text) for user_app_init
    arch_hibernation.o(.text) refers to arch_hibernation.o(.text) for set_ldo_ret_trim
    arch_hibernation.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    arch_hibernation.o(.text) refers to rwip.o(.text) for patched_ble_regs_push
    arch_hibernation.o(.text) refers to arch_hibernation.o(.text) for set_ldo_ret_trim
    arch_hibernation.o(.text) refers to arch_hibernation.o(retention_mem_area0) for retention_mem_area0
    arch_hibernation.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    arch_hibernation.o(.text) refers to arch_system.o(.text) for set_xtal32m_trim_value
    arch_hibernation.o(.text) refers to user_periph_setup.o(.text) for periph_init
    arch_hibernation.o(.text) refers to rwip.o(.text) for patched_ble_regs_pop
    arch_hibernation.o(.text) refers to otp_cs.o(otp_cs_booter) for booter_val
    arch_hibernation.o(.text) refers to arch_hibernation.o(retention_mem_area0) for retention_mem_area0
    arch_hibernation.o(.text) refers to arch_system.o(retention_mem_area0) for last_temp
    arch_rom.o(.text) refers to patch.o(.text) for patch_global_vars_init
    arch_rom.o(.text) refers to trng.o(trng_state) for trng_state_val
    arch_rom.o(.text) refers to arch_rom.o(.constdata) for .constdata
    arch_rom.o(.text) refers to arch_rom.o(retention_mem_area0) for retention_mem_area0
    arch_rom.o(.text) refers to jump_table.o(.constdata) for rom_func_addr_table_var
    arch_rom.o(.text) refers to jump_table.o(.constdata) for rom_cfg_table_var
    otp_cs.o(.text) refers (Weak) to rf_531.o(.text) for rf_pa_pwr_get
    otp_cs.o(.text) refers (Weak) to rf_531.o(.text) for rf_pa_pwr_get
    otp_cs.o(.text) refers to otp_cs.o(retention_mem_area0) for retention_mem_area0
    otp_cs.o(.text) refers to otp_cs.o(otp_cs_booter) for otp_cs_booter
    otp_cs.o(.text) refers to otp_cs.o(.bss) for .bss
    otp_cs.o(.text) refers to otp_cs.o(retention_mem_area0) for retention_mem_area0
    otp_cs.o(.text) refers to otp_cs.o(retention_mem_area0) for retention_mem_area0
    otp_cs.o(.text) refers to otp_cs.o(retention_mem_area0) for retention_mem_area0
    syscntl.o(.text) refers (Weak) to hw_otpc_531.o(.text) for hw_otpc_is_dcdc_reserved
    syscntl.o(.text) refers to syscntl.o(.bss) for .bss
    syscntl.o(.text) refers (Weak) to hw_otpc_531.o(.text) for hw_otpc_is_dcdc_reserved
    syscntl.o(.text) refers to syscntl.o(.text) for dcdc_cfg
    gpio.o(.text) refers to gpio.o(retention_mem_area0) for retention_mem_area0
    gpio.o(.text) refers to gpio.o(.text) for gpioshift16
    hw_otpc_531.o(.text) refers to syscntl.o(.text) for syscntl_dcdc_get_level
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(retention_mem_area0) for retention_mem_area0
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.constdata) for .constdata
    hw_otpc_531.o(.text) refers to syscntl.o(.bss) for syscntl_dcdc_state
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_prog
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_read_verif
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    hw_otpc_531.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_enter_mode
    uart.o(.text) refers to ring_buf.o(.text) for RingBuf_get
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.text) for uart_read_byte
    uart.o(.text) refers to uart.o(.text) for uart_read_buffer
    uart.o(.text) refers to uart.o(.text) for __NVIC_DisableIRQ
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to ring_buf.o(.text) for RingBuf_DeInit
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.text) for __NVIC_DisableIRQ
    uart.o(.text) refers to ring_buf.o(.text) for RingBuf_Init
    uart.o(.text) refers to uart.o(.text) for __NVIC_DisableIRQ
    uart.o(.text) refers to uart.o(.bss) for .bss
    uart.o(.text) refers to uart.o(.text) for uart_enable_flow_control
    trng.o(.text) refers to trng.o(trng_state) for trng_state
    trng.o(.text) refers to arch_system.o(.bss) for otp_hdr_timestamp
    trng.o(.text) refers to trng.o(.bss) for .bss
    adc_531.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_25_cal
    adc_531.o(.text) refers to adc_531.o(.bss) for .bss
    adc_531.o(.text) refers to adc_531.o(retention_mem_area0) for retention_mem_area0
    adc_531.o(.text) refers to adc_531.o(.text) for __NVIC_DisableIRQ
    adc_531.o(.text) refers to adc_531.o(.text) for adc_input_shift_enable
    adc_531.o(.text) refers to adc_531.o(retention_mem_area0) for retention_mem_area0
    adc_531.o(.text) refers to adc_531.o(.text) for __NVIC_DisableIRQ
    adc_531.o(.text) refers to adc_531.o(retention_mem_area0) for retention_mem_area0
    adc_531.o(.text) refers to adc_531.o(.text) for adc_start
    adc_531.o(.text) refers to adc_531.o(.text) for adc_init
    adc_531.o(.text) refers to adc_531.o(.text) for adc_get_sample
    adc_531.o(.text) refers to adc_531.o(.text) for adc_correction_apply
    adc_531.o(.text) refers to adc_531.o(.bss) for .bss
    adc_531.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_offsh_ge
    adc_531.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_offsh_offset
    adc_531.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_single_ge
    rwble.o(.text) refers to arch_system.o(.text) for lld_sleep_lpcycles_2_us_sel_func
    rwble.o(.text) refers to syscntl.o(.text) for syscntl_use_highest_amba_clocks
    rwble.o(.text) refers to rwip.o(.text) for patched_ble_regs_pop
    rwble.o(.text) refers to rf_531.o(.text) for rf_adplldig_deactivate
    rwble.o(.text) refers to rwble.o(retention_mem_area0) for retention_mem_area0
    rwble.o(.text) refers to rwip.o(retention_mem_area0) for slp_period_retained
    rwble.o(.text) refers to arch_system.o(retention_mem_area0) for clk_freq_trim_reg_value
    rwip.o(.text) refers to arch_sleep.o(.text) for arch_ble_ext_wakeup_get
    rwip.o(.text) refers to arch_system.o(.text) for set_sleep_delay
    rwip.o(.text) refers to syscntl.o(.text) for syscntl_cfg_xtal32m_amp_reg
    rwip.o(.text) refers to rwip.o(retention_mem_area0) for retention_mem_area0
    rwip.o(.text) refers to arch_system.o(retention_mem_area0) for xtal_wait_trim
    rwip.o(.text) refers to arch_system.o(.bss) for twirq_reset_value
    rwip.o(.text) refers to rwip.o(.bss) for .bss
    rwip.o(.text) refers to rwble.o(retention_mem_area0) for ble_finetim_corr
    ble_arp.o(.text) refers (Weak) to rf_531.o(.text) for rf_pa_pwr_get
    ble_arp.o(.text) refers to syscntl.o(.text) for syscntl_cfg_xtal32m_amp_reg
    ble_arp.o(.text) refers to rf_531.o(.text) for rf_power_up
    ble_arp.o(.text) refers to otp_cs.o(.text) for otp_cs_load_pd_rad
    ble_arp.o(.text) refers to ble_arp.o(.constdata) for .constdata
    rf_531.o(.text) refers to rf_531.o(retention_mem_area0) for retention_mem_area0
    rf_531.o(.text) refers to rf_531.o(.data) for .data
    rf_531.o(.text) refers to rf_531.o(.bss) for .bss
    rf_531.o(.text) refers to rf_531.o(.text) for en_adpll_tx
    rf_531.o(.text) refers to rf_531.o(.bss) for .bss
    rf_531.o(.text) refers to rf_531.o(.bss) for .bss
    rf_531.o(.text) refers to rf_531.o(.bss) for .bss
    rf_531.o(.text) refers to rf_531.o(retention_mem_area0) for retention_mem_area0
    rf_531.o(.text) refers to rf_531.o(retention_mem_area0) for retention_mem_area0
    custs1.o(.constdata) refers to user_custs1_def.o(.constdata) for custs1_services
    custs1.o(.constdata) refers to user_custs1_def.o(.constdata) for custs1_services_size
    custs1.o(.constdata) refers to user_custs1_def.o(.constdata) for custs1_att_db
    custs1.o(.constdata) refers to app_customs.o(.text) for custs_get_func_callbacks
    custs1_task.o(.text) refers to app_customs.o(.text) for custs_get_func_callbacks
    custs1_task.o(.constdata) refers to custs1_task.o(.text) for gattc_write_req_ind_handler
    custs1_task.o(.constdata) refers to custs1_task.o(.constdata) for custs1_default_state
    prf.o(.text) refers to prf.o(retention_mem_area0) for retention_mem_area0
    prf.o(.text) refers to custs1_task.o(.constdata) for custs1_default_handler
    prf.o(.constdata) refers to prf.o(.constdata) for prf_if
    prf.o(.constdata) refers to prf.o(retention_mem_area0) for prf_env
    app_default_handlers.o(.text) refers to app.o(.text) for app_easy_gap_undirected_advertise_start
    app_default_handlers.o(.text) refers to app_diss.o(.text) for app_dis_init
    app_default_handlers.o(.text) refers to arch_sleep.o(.text) for arch_set_sleep_mode
    app_default_handlers.o(.text) refers to app.o(.text) for app_prf_enable
    app_default_handlers.o(.text) refers to user_peripheral.o(.text) for user_app_adv_start
    app_default_handlers.o(.text) refers to hw_otpc_531.o(.text) for hw_otpc_init
    app_default_handlers.o(.text) refers to otp_cs.o(.text) for otp_cs_get_adc_single_offset
    app_default_handlers.o(.text) refers to hash.o(.text) for hash
    app_default_handlers.o(.text) refers to app_easy_security.o(.text) for app_easy_security_send_pairing_rsp
    app_default_handlers.o(.text) refers to app_security.o(.text) for app_sec_gen_ltk
    app_default_handlers.o(.text) refers to app.o(retention_mem_area0) for app_env
    app_default_handlers.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_default_handlers.o(.text) refers to user_peripheral.o(.text) for user_app_adv_start
    app_default_handlers.o(.text) refers to app_easy_security.o(.text) for app_easy_security_tk_exch
    app_default_handlers.o(.text) refers to app_easy_security.o(.text) for app_easy_security_bdb_add_entry
    app_default_handlers.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_default_handlers.o(.constdata) refers to user_peripheral.o(.text) for user_app_adv_start
    app.o(.text) refers to strlen.o(.text) for strlen
    app.o(.text) refers to app_default_handlers.o(.text) for default_app_generate_unique_static_random_addr
    app.o(.text) refers to app.o(.constdata) for .constdata
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to user_custs_config.o(.constdata) for cust_prf_funcs
    app.o(.text) refers to user_custs1_impl.o(text) for device_config
    app.o(.text) refers to strlen.o(.text) for strlen
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to user_custs1_impl.o(text) for device_config
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.constdata) for .constdata
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_directed_advertise_start_create_msg
    app.o(.text) refers to app.o(.text) for app_easy_gap_directed_advertise_start_create_msg
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_non_connectable_advertise_start_create_msg
    app.o(.text) refers to app.o(.text) for app_easy_gap_non_connectable_advertise_start_create_msg
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_cancel
    app.o(.text) refers to app.o(.text) for app_easy_gap_undirected_advertise_start
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_advertise_stop_handler
    app.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_cancel
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_param_update_msg_create
    app.o(.text) refers to app.o(.text) for app_easy_gap_start_connection_to_msg_create
    app.o(.text) refers to app.o(.text) for app_easy_gap_start_connection_to_msg_create
    app.o(.text) refers to app.o(.text) for app_easy_gap_start_connection_to_msg_create
    app.o(.text) refers to app.o(retention_mem_area0) for retention_mem_area0
    app.o(.text) refers to app.o(.text) for app_easy_gap_dev_config_create_msg
    app.o(.constdata) refers to app_diss.o(.text) for app_diss_create_db
    app.o(.constdata) refers to app_task.o(retention_mem_area0) for app_state
    app.o(.constdata) refers to user_peripheral.o(.text) for user_app_connection
    app.o(.constdata) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_task.o(.text) refers to user_peripheral.o(.text) for user_catch_rest_hndl
    app_task.o(.text) refers to app.o(.text) for app_easy_gap_dev_configure
    app_task.o(.text) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_task.o(.text) refers to strlen.o(.text) for strlen
    app_task.o(.text) refers to app.o(retention_mem_area0) for app_env
    app_task.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_task.o(.text) refers to user_custs1_impl.o(text) for device_config
    app_task.o(.text) refers to app_task.o(.constdata) for .constdata
    app_task.o(.constdata) refers to user_peripheral.o(.text) for user_app_connection
    app_task.o(.constdata) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_task.o(.constdata) refers to app_task.o(.text) for gapm_device_ready_ind_handler
    app_security.o(.text) refers to app_utils.o(.text) for app_fill_random_byte_array
    app_security.o(.text) refers to app_security.o(retention_mem_area0) for retention_mem_area0
    app_security_task.o(.text) refers to app_default_handlers.o(.text) for default_app_on_pairing_request
    app_security_task.o(.text) refers to user_peripheral.o(.text) for user_app_on_tk_exch
    app_security_task.o(.text) refers to app.o(retention_mem_area0) for app_env
    app_security_task.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_security_task.o(.text) refers to app_security_task.o(.constdata) for .constdata
    app_security_task.o(.constdata) refers to user_peripheral.o(.text) for user_app_connection
    app_security_task.o(.constdata) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_security_task.o(.constdata) refers to app_security_task.o(.text) for gapc_bond_req_ind_handler
    app_diss_task.o(.text) refers to user_custs1_impl.o(.text) for Get_Software_Revision
    app_diss_task.o(.text) refers to printf1.o(i.__0sprintf$1) for __2sprintf
    app_diss_task.o(.text) refers to strlen.o(.text) for strlen
    app_diss_task.o(.text) refers to app_diss_task.o(.constdata) for .constdata
    app_diss_task.o(.constdata) refers to app_diss_task.o(.text) for diss_value_req_ind_handler
    app_entry_point.o(.constdata) refers to user_peripheral.o(.text) for user_app_connection
    app_entry_point.o(.constdata) refers to app_default_handlers.o(.text) for default_app_on_set_dev_config_complete
    app_entry_point.o(.constdata) refers to app_task.o(.text) for app_gap_process_handler
    app_entry_point.o(.constdata) refers to app_easy_timer.o(.text) for app_timer_api_process_handler
    app_entry_point.o(.constdata) refers to app_security_task.o(.text) for app_sec_process_handler
    app_entry_point.o(.constdata) refers to app_diss_task.o(.text) for app_diss_process_handler
    app_entry_point.o(.constdata) refers to app_customs_task.o(.text) for app_custs1_process_handler
    app_entry_point.o(.constdata) refers to app.o(.text) for app_easy_gap_dev_configure
    app_entry_point.o(.constdata) refers to app_entry_point.o(.constdata) for user_app_callbacks
    app_entry_point.o(.constdata) refers to user_peripheral.o(.text) for user_catch_rest_hndl
    app_entry_point.o(.constdata) refers to app_entry_point.o(.constdata) for app_process_handlers
    app_entry_point.o(.constdata) refers to app.o(retention_mem_area0) for app_env
    app_entry_point.o(.constdata) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_entry_point.o(.constdata) refers to app.o(retention_mem_area0) for app_prf_srv_perm
    app_msg_utils.o(.text) refers to app_msg_utils.o(.text) for app_check_BLE_active
    app_msg_utils.o(.text) refers to arch_sleep.o(.text) for arch_ble_force_wakeup
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_find_idx
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_find_idx
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_modify
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_set
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_free_callback
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(.text) for app_easy_msg_find_idx
    app_easy_msg_utils.o(.text) refers to app_easy_msg_utils.o(retention_mem_area0) for retention_mem_area0
    app_easy_security.o(.text) refers to app_security.o(.text) for app_sec_gen_csrk
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_init
    app_easy_security.o(.text) refers to app_easy_security.o(retention_mem_area0) for retention_mem_area0
    app_easy_security.o(.text) refers to app_easy_security.o(.constdata) for .constdata
    app_easy_security.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for pairing_rsp_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for tk_exch_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for csrk_exch_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for ltk_exch_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for ltk_exch_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for encrypt_cfm_create_msg
    app_easy_security.o(.text) refers to app_easy_security.o(retention_mem_area0) for retention_mem_area0
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for app_easy_security_request_get_active
    app_easy_security.o(.text) refers to app_easy_security.o(retention_mem_area0) for retention_mem_area0
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_stored_irks
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_number_of_stored_irks
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_number_of_stored_irks
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_stored_irks
    app_easy_security.o(.text) refers to app.o(retention_mem_area0) for app_env
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_size
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_device_info_from_slot
    app_easy_security.o(.text) refers to app_bond_db.o(.text) for default_app_bdb_get_number_of_stored_irks
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for app_easy_security_bdb_get_device_info_from_slot
    app_easy_security.o(.text) refers to app_easy_security.o(.text) for app_easy_security_ral_op
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_init
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_get_size
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_get_number_of_stored_irks
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_get_stored_irks
    app_easy_security.o(.constdata) refers to app_bond_db.o(.text) for default_app_bdb_get_device_info_from_slot
    app_easy_timer.o(.text) refers to app_msg_utils.o(.text) for app_check_BLE_active
    app_easy_timer.o(.text) refers to arch_sleep.o(.text) for arch_ble_force_wakeup
    app_easy_timer.o(.text) refers to app_easy_timer.o(retention_mem_area0) for retention_mem_area0
    app_easy_timer.o(.text) refers to app_easy_timer.o(.text) for timer_canceled_handler
    app_easy_timer.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_cancel
    app_easy_timer.o(.text) refers to app_easy_timer.o(retention_mem_area0) for retention_mem_area0
    app_easy_timer.o(.text) refers to app_easy_timer.o(.text) for timer_canceled_handler
    app_customs.o(.text) refers to user_custs_config.o(.constdata) for cust_prf_funcs
    app_bond_db.o(.text) refers to user_custs1_impl.o(.text) for GetBondInfo
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to user_custs1_impl.o(.text) for notify_stm
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    app_bond_db.o(.text) refers to uart.o(.text) for uart_send
    app_bond_db.o(.text) refers to app_bond_db.o(retention_mem_area0) for retention_mem_area0
    user_custs_config.o(.constdata) refers to user_custs1_def.o(.constdata) for custs1_att_db
    user_custs_config.o(.constdata) refers to app_customs.o(.text) for app_custs1_create_db
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for att_decl_svc
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for Ecg_Service
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for att_decl_char
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for ECG_SAMPLES_UUID
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for att_desc_cfg
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for PPG_SAMPLES_UUID
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for att_desc_user_desc
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.conststring) for .conststring
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for Vital_Service
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for VITAL_UUID
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for Alert_Service
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for ALERT_STATUS_UUID
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for User_Info_Service
    user_custs1_def.o(.constdata) refers to user_custs1_def.o(.constdata) for USR_INFO_UUID_128
    user_periph_setup.o(.text) refers to gpio.o(.text) for GPIO_ConfigurePin
    user_periph_setup.o(.text) refers to gpio.o(.text) for GPIO_ConfigurePin
    user_periph_setup.o(.text) refers to syscntl.o(.text) for syscntl_dcdc_turn_on_in_boost
    user_periph_setup.o(.text) refers to patch.o(.text) for patch_func
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_initialize
    user_periph_setup.o(.text) refers to user_periph_setup.o(.data) for .data
    user_periph_setup.o(.text) refers to uart.o(.text) for Close_Uart
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_disable
    user_periph_setup.o(.text) refers to uart.o(.text) for Open_Uart
    user_periph_setup.o(.text) refers to user_periph_setup.o(.data) for .data
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_initialize
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_register_tx_cb
    user_periph_setup.o(.text) refers to uart.o(.text) for Open_Uart
    user_periph_setup.o(.text) refers to gpio.o(.text) for GPIO_ConfigurePin
    user_periph_setup.o(.text) refers to user_periph_setup.o(.data) for .data
    user_periph_setup.o(.text) refers to user_periph_setup.o(.text) for uart_send_cb
    user_periph_setup.o(.text) refers to uart.o(.text) for Close_Uart
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_disable
    user_periph_setup.o(.text) refers to user_custs1_impl.o(.text) for notify_stm
    user_periph_setup.o(.text) refers to user_periph_setup.o(.bss) for .bss
    user_periph_setup.o(.text) refers to uart.o(.text) for uart_read_byte
    user_periph_setup.o(.text) refers to user_periph_setup.o(.text) for send_ppg_over_ble
    user_periph_setup.o(.text) refers to user_periph_setup.o(.bss) for .bss
    user_periph_setup.o(.text) refers to user_periph_setup.o(.text) for simple_uart_rx_handler
    user_custs1_impl.o(.text) refers to comm_manager.o(.text) for Com_Send_Data
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(text) for text
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(free_area) for free_area
    user_custs1_impl.o(.text) refers to app.o(retention_mem_area0) for app_env
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.constdata) for .constdata
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_custs1_impl.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.text) for ResetCommLock_cb
    user_custs1_impl.o(.text) refers to user_custs1_impl.o(.bss) for .bss
    user_peripheral.o(.text) refers to user_custs1_impl.o(.text) for notify_stm
    user_peripheral.o(.text) refers to transportmanager.o(.text) for GetBufferXferContext
    user_peripheral.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer
    user_peripheral.o(.text) refers to user_peripheral.o(.bss) for .bss
    user_peripheral.o(.text) refers to user_peripheral.o(retention_mem_area0) for retention_mem_area0
    user_peripheral.o(.text) refers to user_peripheral.o(.data) for .data
    user_peripheral.o(.text) refers to app.o(.text) for app_easy_gap_param_update_start
    user_peripheral.o(.text) refers to gpio.o(.text) for GPIO_EnableIRQ
    user_peripheral.o(.text) refers to app_default_handlers.o(.text) for default_app_on_init
    user_peripheral.o(.text) refers to user_periph_setup.o(.text) for init_simple_ppg_uart
    user_peripheral.o(.text) refers to app_easy_security.o(.text) for app_easy_security_bdb_init
    user_peripheral.o(.text) refers to app_security.o(.text) for app_sec_gen_tk
    user_peripheral.o(.text) refers to serialinterface.o(.text) for uart_trigger
    user_peripheral.o(.text) refers to app_security.o(retention_mem_area0) for app_sec_env
    user_peripheral.o(.text) refers to app.o(retention_mem_area0) for app_env
    user_peripheral.o(.text) refers to user_custs1_impl.o(.bss) for Comm_Lock
    user_peripheral.o(.text) refers to user_custs1_impl.o(.text) for Send_To_Gatt_Client
    user_peripheral.o(.text) refers to user_custs1_impl.o(.text) for Send_To_Gatt_Client
    scheduler.o(.text) refers to user_custs1_impl.o(.text) for notify_stm
    scheduler.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer
    scheduler.o(.text) refers to scheduler.o(retention_mem_area0) for retention_mem_area0
    scheduler.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer
    scheduler.o(.text) refers to scheduler.o(retention_mem_area0) for retention_mem_area0
    scheduler.o(.text) refers to scheduler.o(.text) for SchedulerCallback
    scheduler.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_modify
    scheduler.o(.text) refers to scheduler.o(retention_mem_area0) for retention_mem_area0
    scheduler.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_cancel
    scheduler.o(.text) refers to scheduler.o(retention_mem_area0) for retention_mem_area0
    scheduler.o(.text) refers to scheduler.o(.bss) for .bss
    comm_task.o(.text) refers to primitivequeue.o(.text) for InitQueue
    comm_task.o(.text) refers to transportqueue.o(.text) for InitTriggerQueue
    comm_task.o(.text) refers to primitivemanager.o(.text) for resetPrimitiveManager
    comm_task.o(.text) refers to transportmanager.o(.text) for resetTransportManager
    comm_task.o(.text) refers to comm_manager.o(.text) for RunMainLoop
    comm_task.o(.text) refers to app_easy_timer.o(.text) for app_easy_timer_modify
    comm_task.o(.text) refers to comm_task.o(.constdata) for .constdata
    comm_task.o(.text) refers to comm_task.o(retention_mem_area0) for retention_mem_area0
    comm_task.o(.text) refers to comm_task.o(.bss) for .bss
    comm_task.o(.constdata) refers to comm_task.o(.text) for comm_manager
    comm_task.o(.constdata) refers to comm_task.o(.constdata) for comm_default_state
    comm_task.o(.constdata) refers to comm_task.o(.constdata) for comm_handler
    comm_task.o(.constdata) refers to comm_task.o(retention_mem_area0) for comm_state
    serialinterface.o(.text) refers to uart.o(.text) for uart_send
    serialinterface.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    serialinterface.o(.text) refers to gpio.o(.text) for GPIO_GetPinStatus
    serialinterface.o(.text) refers to comm_manager.o(.text) for COM_BreakEvent
    serialinterface.o(.text) refers to serialinterface.o(.data) for .data
    serialinterface.o(.text) refers to user_periph_setup.o(.text) for UART_OPEN_PORT_INIT
    serialinterface.o(.text) refers to user_periph_setup.o(.text) for UART_CLOSE_PORT_INIT
    serialinterface.o(.data) refers to serialinterface.o(.text) for uart_error_cb
    serialinterface.o(.data) refers to serialinterface.o(.text) for uart_send_cb
    upperlayerinterface.o(.text) refers to user_peripheral.o(.text) for updateMacIDPayload
    upperlayerinterface.o(.text) refers to transportmanager.o(.text) for GetBufferXferContext
    upperlayerinterface.o(.text) refers to upperlayerinterface.o(.bss) for .bss
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to primitivemanager.o(.text) for PrimitiveMain
    comm_manager.o(.text) refers to transportmanager.o(.text) for GetTransportTrigger
    comm_manager.o(.text) refers to comm_manager.o(.constdata) for .constdata
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to comm_task.o(.text) for triggerCommMngr
    comm_manager.o(.text) refers to primitivemanager.o(.text) for resetPrimitiveManager
    comm_manager.o(.text) refers to transportmanager.o(.text) for resetTransportManager
    comm_manager.o(.text) refers to serialinterface.o(.text) for Close_Port
    primitivemanager.o(.text) refers to transportmanager.o(.text) for SetTransportTrigger
    primitivemanager.o(.text) refers to comm_task.o(.text) for stopCRTimer
    primitivemanager.o(.text) refers to primitivemanager.o(.bss) for .bss
    primitivemanager.o(.text) refers to primitivemanager.o(.data) for .data
    primitivemanager.o(.text) refers to primitivemanager.o(.constdata) for .constdata
    primitivemanager.o(.text) refers to primitivequeue.o(.text) for isCmdTxEmpty
    primitivemanager.o(.text) refers to comm_manager.o(.text) for WriteCmd
    primitivemanager.o(.text) refers to upperlayerinterface.o(.text) for BLE_ExecuteResponse
    primitivemanager.o(.text) refers to primitivemanager.o(.bss) for .bss
    primitivemanager.o(.text) refers to primitivemanager.o(.data) for .data
    primitivemanager.o(.text) refers to primitivemanager.o(.text) for resetPrimitiveManagerInd
    primitivemanager.o(.text) refers to primitivemanager.o(.data) for .data
    primitivemanager.o(.text) refers to primitivemanager.o(.bss) for .bss
    primitivemanager.o(.constdata) refers to primitivemanager.o(.text) for cmd_tx_event
    primitivequeue.o(.text) refers to primitivequeue.o(.bss) for .bss
    primitivequeue.o(.text) refers to primitivequeue.o(.data) for .data
    transportmanager.o(.text) refers to serialinterface.o(.text) for sendBreakCmd
    transportmanager.o(.text) refers to uart.o(.text) for rbuf_get
    transportmanager.o(.text) refers to primitivemanager.o(.text) for resetPrimitiveMaster
    transportmanager.o(.text) refers to comm_task.o(.text) for Comm_Sleep_Set
    transportmanager.o(.text) refers to transportmanager.o(.bss) for .bss
    transportmanager.o(.text) refers to transportmanager.o(.constdata) for .constdata
    transportmanager.o(.text) refers to transportmanager.o(.data) for .data
    transportmanager.o(.text) refers to upperlayerinterface.o(.text) for getMtuSize
    transportmanager.o(.text) refers to transportqueue.o(.text) for dequeueTrigger
    transportmanager.o(.text) refers to transportmanager.o(.bss) for .bss
    transportmanager.o(.constdata) refers to transportmanager.o(.text) for handle_close
    transportqueue.o(.text) refers to transportqueue.o(.bss) for .bss
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_da14531.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_da14531.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to arch_main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to arch_main.o(.text) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz


==============================================================================

Removing Unused input sections from the image.

    Removing system_da14531.o(.text), (16 bytes).
    Removing system_da14531.o(.data), (4 bytes).
    Removing startup_da14531.o(HEAP), (256 bytes).
    Removing arch_main.o(.constdata), (28 bytes).
    Removing jump_table.o(.text), (2 bytes).
    Removing arch_sleep.o(.text), (276 bytes).
    Removing arch_sleep.o(.text), (80 bytes).
    Removing arch_sleep.o(.text), (36 bytes).
    Removing arch_sleep.o(.text), (12 bytes).
    Removing arch_sleep.o(.text), (12 bytes).
    Removing arch_sleep.o(.text), (12 bytes).
    Removing arch_system.o(.text), (60 bytes).
    Removing arch_system.o(.text), (56 bytes).
    Removing arch_system.o(.text), (2 bytes).
    Removing arch_system.o(.constdata), (28 bytes).
    Removing arch_system.o(.data), (4 bytes).
    Removing arch_system.o(retention_mem_area0), (1 bytes).
    Removing arch_system.o(retention_mem_area0), (2 bytes).
    Removing arch_system.o(retention_mem_area0), (2 bytes).
    Removing arch_system.o(retention_mem_area0), (8 bytes).
    Removing arch_system.o(retention_mem_area0), (8 bytes).
    Removing arch_system.o(retention_mem_area0), (8 bytes).
    Removing arch_system.o(retention_mem_area0), (8 bytes).
    Removing arch_hibernation.o(.text), (300 bytes).
    Removing arch_hibernation.o(.text), (336 bytes).
    Removing arch_hibernation.o(.text), (448 bytes).
    Removing arch_hibernation.o(.text), (40 bytes).
    Removing arch_hibernation.o(retention_mem_area0), (8 bytes).
    Removing otp_cs.o(.text), (12 bytes).
    Removing otp_cs.o(.text), (12 bytes).
    Removing otp_cs.o(.text), (12 bytes).
    Removing syscntl.o(.text), (36 bytes).
    Removing syscntl.o(.text), (40 bytes).
    Removing syscntl.o(.text), (84 bytes).
    Removing syscntl.o(.text), (44 bytes).
    Removing gpio.o(.text), (24 bytes).
    Removing gpio.o(.text), (24 bytes).
    Removing gpio.o(.text), (32 bytes).
    Removing gpio.o(.text), (144 bytes).
    Removing gpio.o(.text), (36 bytes).
    Removing gpio.o(.text), (40 bytes).
    Removing gpio.o(.text), (12 bytes).
    Removing gpio.o(.text), (16 bytes).
    Removing gpio.o(.text), (16 bytes).
    Removing hw_otpc_531.o(.text), (68 bytes).
    Removing hw_otpc_531.o(.text), (22 bytes).
    Removing hw_otpc_531.o(.text), (52 bytes).
    Removing hw_otpc_531.o(.text), (54 bytes).
    Removing hw_otpc_531.o(.text), (40 bytes).
    Removing hw_otpc_531.o(.text), (52 bytes).
    Removing uart.o(.text), (4 bytes).
    Removing uart.o(.text), (4 bytes).
    Removing uart.o(.text), (4 bytes).
    Removing uart.o(.text), (64 bytes).
    Removing uart.o(.text), (36 bytes).
    Removing uart.o(.text), (36 bytes).
    Removing uart.o(.text), (36 bytes).
    Removing uart.o(.text), (12 bytes).
    Removing uart.o(.text), (14 bytes).
    Removing uart.o(.text), (28 bytes).
    Removing uart.o(.text), (124 bytes).
    Removing uart.o(.text), (16 bytes).
    Removing uart.o(.text), (44 bytes).
    Removing uart.o(.text), (108 bytes).
    Removing uart.o(.text), (36 bytes).
    Removing uart.o(.text), (108 bytes).
    Removing adc_531.o(.text), (32 bytes).
    Removing adc_531.o(.text), (24 bytes).
    Removing adc_531.o(.text), (16 bytes).
    Removing adc_531.o(.text), (40 bytes).
    Removing adc_531.o(.text), (56 bytes).
    Removing adc_531.o(.text), (44 bytes).
    Removing adc_531.o(.text), (16 bytes).
    Removing adc_531.o(.text), (28 bytes).
    Removing adc_531.o(.text), (220 bytes).
    Removing adc_531.o(.text), (40 bytes).
    Removing adc_531.o(.text), (92 bytes).
    Removing adc_531.o(.text), (116 bytes).
    Removing rwble.o(retention_mem_area0), (1 bytes).
    Removing rwip.o(retention_mem_area0), (4 bytes).
    Removing ble_arp.o(.constdata), (4 bytes).
    Removing rf_531.o(.text), (276 bytes).
    Removing rf_531.o(.text), (12 bytes).
    Removing rf_531.o(.text), (12 bytes).
    Removing rf_531.o(.text), (404 bytes).
    Removing rf_531.o(.text), (2 bytes).
    Removing rf_531.o(.text), (12 bytes).
    Removing rf_531.o(.text), (16 bytes).
    Removing rf_531.o(.text), (32 bytes).
    Removing rf_531.o(.text), (28 bytes).
    Removing rf_531.o(.text), (28 bytes).
    Removing rf_531.o(.text), (80 bytes).
    Removing rf_531.o(.text), (32 bytes).
    Removing rf_531.o(.text), (24 bytes).
    Removing app_default_handlers.o(.text), (8 bytes).
    Removing app_default_handlers.o(.text), (8 bytes).
    Removing app_default_handlers.o(.text), (48 bytes).
    Removing app_default_handlers.o(.text), (68 bytes).
    Removing app_default_handlers.o(.text), (28 bytes).
    Removing app_default_handlers.o(.constdata), (12 bytes).
    Removing app_default_handlers.o(.constdata), (4 bytes).
    Removing app.o(.text), (220 bytes).
    Removing app.o(.text), (96 bytes).
    Removing app.o(.text), (80 bytes).
    Removing app.o(.text), (28 bytes).
    Removing app.o(.text), (8 bytes).
    Removing app.o(.text), (32 bytes).
    Removing app.o(.text), (8 bytes).
    Removing app.o(.text), (40 bytes).
    Removing app.o(.text), (48 bytes).
    Removing app.o(.text), (28 bytes).
    Removing app.o(.text), (8 bytes).
    Removing app.o(.text), (8 bytes).
    Removing app.o(.text), (38 bytes).
    Removing app.o(.text), (24 bytes).
    Removing app.o(.text), (8 bytes).
    Removing app.o(.text), (32 bytes).
    Removing app.o(.constdata), (12 bytes).
    Removing app.o(.constdata), (74 bytes).
    Removing app.o(.constdata), (140 bytes).
    Removing app_task.o(.constdata), (140 bytes).
    Removing app_security_task.o(.constdata), (140 bytes).
    Removing app_msg_utils.o(.text), (24 bytes).
    Removing app_easy_msg_utils.o(.text), (32 bytes).
    Removing app_easy_msg_utils.o(.text), (52 bytes).
    Removing app_easy_msg_utils.o(.text), (36 bytes).
    Removing app_easy_msg_utils.o(.text), (32 bytes).
    Removing app_easy_msg_utils.o(.text), (20 bytes).
    Removing app_easy_msg_utils.o(.text), (24 bytes).
    Removing app_easy_msg_utils.o(.text), (56 bytes).
    Removing app_easy_msg_utils.o(.text), (24 bytes).
    Removing app_easy_msg_utils.o(retention_mem_area0), (24 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (44 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (52 bytes).
    Removing app_easy_security.o(.text), (28 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (80 bytes).
    Removing app_easy_security.o(.text), (88 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (2 bytes).
    Removing app_easy_security.o(.text), (8 bytes).
    Removing app_easy_security.o(.text), (46 bytes).
    Removing app_easy_security.o(.constdata), (32 bytes).
    Removing app_easy_timer.o(.text), (44 bytes).
    Removing app_customs.o(.text), (2 bytes).
    Removing app_customs_common.o(.text), (4 bytes).
    Removing app_bond_db.o(.text), (4 bytes).
    Removing app_bond_db.o(.text), (228 bytes).
    Removing app_bond_db.o(.text), (36 bytes).
    Removing app_bond_db.o(.text), (56 bytes).
    Removing app_bond_db.o(.text), (72 bytes).
    Removing app_bond_db.o(.text), (60 bytes).
    Removing app_utils.o(.text), (20 bytes).
    Removing app_easy_whitelist.o(.text), (68 bytes).
    Removing user_custs1_def.o(.constdata), (2 bytes).
    Removing user_periph_setup.o(.text), (38 bytes).
    Removing user_periph_setup.o(.text), (56 bytes).
    Removing user_periph_setup.o(.text), (2 bytes).
    Removing user_periph_setup.o(.text), (88 bytes).
    Removing user_periph_setup.o(.text), (24 bytes).
    Removing user_periph_setup.o(.text), (116 bytes).
    Removing user_periph_setup.o(.text), (124 bytes).
    Removing user_periph_setup.o(.text), (24 bytes).
    Removing user_periph_setup.o(.bss), (34 bytes).
    Removing user_custs1_impl.o(.text), (12 bytes).
    Removing user_custs1_impl.o(.text), (68 bytes).
    Removing user_custs1_impl.o(.text), (12 bytes).
    Removing user_custs1_impl.o(.text), (68 bytes).
    Removing user_custs1_impl.o(.text), (76 bytes).
    Removing user_custs1_impl.o(.text), (20 bytes).
    Removing user_custs1_impl.o(.text), (20 bytes).
    Removing user_custs1_impl.o(.text), (12 bytes).
    Removing user_custs1_impl.o(.bss), (4 bytes).
    Removing user_custs1_impl.o(.bss), (49 bytes).
    Removing user_custs1_impl.o(.bss), (2 bytes).
    Removing user_custs1_impl.o(.bss), (4 bytes).
    Removing user_peripheral.o(.text), (84 bytes).
    Removing user_peripheral.o(.text), (28 bytes).
    Removing user_peripheral.o(.constdata), (12 bytes).
    Removing scheduler.o(.text), (36 bytes).
    Removing scheduler.o(.text), (36 bytes).
    Removing scheduler.o(.text), (36 bytes).
    Removing scheduler.o(.text), (32 bytes).
    Removing scheduler.o(.bss), (1 bytes).
    Removing scheduler.o(.bss), (1 bytes).
    Removing scheduler.o(.bss), (2 bytes).
    Removing scheduler.o(retention_mem_area0), (8 bytes).
    Removing ring_buf.o(.text), (16 bytes).
    Removing ring_buf.o(.text), (4 bytes).
    Removing ring_buf.o(.text), (42 bytes).
    Removing ring_buf.o(.text), (34 bytes).
    Removing ring_buf.o(.bss), (4 bytes).
    Removing comm_task.o(.text), (2 bytes).
    Removing comm_task.o(.text), (2 bytes).
    Removing comm_task.o(.text), (2 bytes).
    Removing serialinterface.o(.text), (2 bytes).
    Removing serialinterface.o(.text), (2 bytes).
    Removing serialinterface.o(.text), (18 bytes).
    Removing comm_manager.o(.text), (2 bytes).
    Removing comm_manager.o(.text), (84 bytes).
    Removing comm_manager.o(.text), (46 bytes).
    Removing comm_manager.o(.text), (46 bytes).
    Removing comm_manager.o(.text), (46 bytes).
    Removing comm_manager.o(.text), (56 bytes).
    Removing comm_manager.o(.text), (18 bytes).
    Removing primitivemanager.o(.text), (28 bytes).
    Removing primitivemanager.o(.text), (80 bytes).
    Removing transportmanager.o(.text), (20 bytes).
    Removing dadd.o(.text), (356 bytes).
    Removing dmul.o(.text), (208 bytes).
    Removing ddiv.o(.text), (240 bytes).
    Removing dfixul.o(.text), (64 bytes).
    Removing cdrcmple.o(.text), (40 bytes).
    Removing depilogue.o(.text), (190 bytes).
    Removing otp_cs.o(i.__ARM_common_switch8), (26 bytes).
    Removing depilogue.o(i.__ARM_clz), (46 bytes).

222 unused section(s) (total 10895 bytes) removed from the image.

==============================================================================

Memory Map of the image

  Image Entry point : 0x00000000

  Load Region LR_IROM1 (Base: 0x07fc0000, Size: 0x000000a8, Max: 0x000000c0, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x07fc0000, Size: 0x000000a8, Max: 0x000000c0, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc0000   0x000000a0   Data   RO          280    RESET               startup_da14531.o
    0x07fc00a0   0x00000008   Data   RW          917    otp_cs_booter       otp_cs.o



  Load Region LR_IROM2 (Base: 0x07fc00c0, Size: 0x00000000, Max: 0x00000050, ABSOLUTE)

    Execution Region ER_IROM2 (Base: 0x07fc00c0, Size: 0x00000050, Max: 0x00000050, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc00c0   0x00000050   Zero   RW            1    ER_IROM2.bss        anon$$obj.o



  Load Region LR_IROM3 (Base: 0x07fc0110, Size: 0x00007668, Max: 0x00008ab0, ABSOLUTE)

    Execution Region ER_IROM3 (Base: 0x07fc0110, Size: 0x00007668, Max: 0x00008ab0, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc0110   0x00000000   Code   RO         2969  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x07fc0110   0x00000004   Code   RO         3233    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x07fc0114   0x00000004   Code   RO         3236    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x07fc0118   0x00000000   Code   RO         3238    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x07fc0118   0x00000000   Code   RO         3240    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x07fc0118   0x00000008   Code   RO         3241    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x07fc0120   0x00000000   Code   RO         3243    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x07fc0120   0x00000000   Code   RO         3245    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x07fc0120   0x00000004   Code   RO         3234    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x07fc0124   0x00000098   Code   RO            3    .text               system_da14531.o
    0x07fc01bc   0x00000050   Code   RO          281    .text               startup_da14531.o
    0x07fc020c   0x00000018   Code   RO          286    .text               hardfault_handler.o
    0x07fc0224   0x00000030   Code   RO          338    .text               nmi_handler.o
    0x07fc0254   0x00000310   Code   RO          376    .text               arch_main.o
    0x07fc0564   0x00000044   Code   RO          578    .text               jump_table.o
    0x07fc05a8   0x000000c0   Code   RO          652    .text               arch_sleep.o
    0x07fc0668   0x000004e8   Code   RO          706    .text               arch_system.o
    0x07fc0b50   0x0000015c   Code   RO          873    .text               arch_rom.o
    0x07fc0cac   0x0000004c   Code   RO          897    .text               hash.o
    0x07fc0cf8   0x000002fc   Code   RO          912    .text               otp_cs.o
    0x07fc0ff4   0x00000010   Code   RO          949    .text               otp_hdr.o
    0x07fc1004   0x00000118   Code   RO          961    .text               syscntl.o
    0x07fc111c   0x0000021c   Code   RO         1001    .text               gpio.o
    0x07fc1338   0x00000184   Code   RO         1078    .text               hw_otpc_531.o
    0x07fc14bc   0x000003e8   Code   RO         1132    .text               uart.o
    0x07fc18a4   0x0000007c   Code   RO         1247    .text               trng.o
    0x07fc1920   0x00000208   Code   RO         1270    .text               adc_531.o
    0x07fc1b28   0x000001bc   Code   RO         1364    .text               rwble.o
    0x07fc1ce4   0x000001ec   Code   RO         1412    .text               rwip.o
    0x07fc1ed0   0x000002e8   Code   RO         1441    .text               ble_arp.o
    0x07fc21b8   0x000004cc   Code   RO         1473    .text               rf_531.o
    0x07fc2684   0x00000150   Code   RO         1624    .text               custs1_task.o
    0x07fc27d4   0x000000a0   Code   RO         1648    .text               prf.o
    0x07fc2874   0x000001f4   Code   RO         1676    .text               app_default_handlers.o
    0x07fc2a68   0x000003dc   Code   RO         1740    .text               app.o
    0x07fc2e44   0x00000278   Code   RO         1880    .text               app_task.o
    0x07fc30bc   0x00000074   Code   RO         1914    .text               app_security.o
    0x07fc3130   0x00000164   Code   RO         1944    .text               app_security_task.o
    0x07fc3294   0x0000003c   Code   RO         1962    .text               app_diss.o
    0x07fc32d0   0x0000012c   Code   RO         1974    .text               app_diss_task.o
    0x07fc33fc   0x00000028   Code   RO         2014    .text               app_msg_utils.o
    0x07fc3424   0x00000258   Code   RO         2086    .text               app_easy_security.o
    0x07fc367c   0x00000002   Code   RO         2205    .text               app_easy_timer.o
    0x07fc367e   0x00000002   PAD
    0x07fc3680   0x00000294   Code   RO         2206    .text               app_easy_timer.o
    0x07fc3914   0x00000060   Code   RO         2230    .text               app_customs.o
    0x07fc3974   0x00000018   Code   RO         2252    .text               app_customs_task.o
    0x07fc398c   0x00000158   Code   RO         2276    .text               app_bond_db.o
    0x07fc3ae4   0x0000000c   Code   RO         2343    .text               app_utils.o
    0x07fc3af0   0x000000ac   Code   RO         2415    .text               user_periph_setup.o
    0x07fc3b9c   0x000001fc   Code   RO         2495    .text               user_custs1_impl.o
    0x07fc3d98   0x00000774   Code   RO         2582    .text               user_peripheral.o
    0x07fc450c   0x0000006c   Code   RO         2657    .text               ring_buf.o
    0x07fc4578   0x00000180   Code   RO         2699    .text               comm_task.o
    0x07fc46f8   0x00000308   Code   RO         2744    .text               serialinterface.o
    0x07fc4a00   0x00000002   Code   RO         2748    .text               serialinterface.o
    0x07fc4a02   0x00000002   PAD
    0x07fc4a04   0x000000a0   Code   RO         2797    .text               upperlayerinterface.o
    0x07fc4aa4   0x0000015c   Code   RO         2812    .text               comm_manager.o
    0x07fc4c00   0x00000bb4   Code   RO         2875    .text               primitivemanager.o
    0x07fc57b4   0x000002d4   Code   RO         2903    .text               primitivequeue.o
    0x07fc5a88   0x000008a4   Code   RO         2925    .text               transportmanager.o
    0x07fc632c   0x00000118   Code   RO         2949    .text               transportqueue.o
    0x07fc6444   0x00000428   Code   RO         2967    .text               da14531.lib(patch.o)
    0x07fc686c   0x0000000e   Code   RO         2970    .text               mc_p.l(strlen.o)
    0x07fc687a   0x00000002   PAD
    0x07fc687c   0x00000024   Code   RO         3261    .text               mc_p.l(init.o)
    0x07fc68a0   0x00000028   Code   RO         3019    i.__0sprintf$1      mc_p.l(printf1.o)
    0x07fc68c8   0x00000030   Code   RO          747    i.__ARM_common_ll_muluu  arch_system.o
    0x07fc68f8   0x00000150   Code   RO         3024    i._printf_core      mc_p.l(printf1.o)
    0x07fc6a48   0x0000000a   Code   RO         3026    i._sputc            mc_p.l(printf1.o)
    0x07fc6a52   0x0000000e   Data   RO          353    .constdata          nvds.o
    0x07fc6a60   0x00000008   Data   RO          354    .constdata          nvds.o
    0x07fc6a68   0x0000017c   Data   RO          579    .constdata          jump_table.o
    0x07fc6be4   0x000000ec   Data   RO          580    .constdata          jump_table.o
    0x07fc6cd0   0x00000008   Data   RO          712    .constdata          arch_system.o
    0x07fc6cd8   0x00000050   Data   RO          874    .constdata          arch_rom.o
    0x07fc6d28   0x0000001c   Data   RO         1085    .constdata          hw_otpc_531.o
    0x07fc6d44   0x00000070   Data   RO         1443    .constdata          ble_arp.o
    0x07fc6db4   0x00000010   Data   RO         1608    .constdata          custs1.o
    0x07fc6dc4   0x00000048   Data   RO         1625    .constdata          custs1_task.o
    0x07fc6e0c   0x00000008   Data   RO         1626    .constdata          custs1_task.o
    0x07fc6e14   0x00000018   Data   RO         1649    .constdata          prf.o
    0x07fc6e2c   0x0000000c   Data   RO         1650    .constdata          prf.o
    0x07fc6e38   0x0000007c   Data   RO         1757    .constdata          app.o
    0x07fc6eb4   0x00000070   Data   RO         1882    .constdata          app_task.o
    0x07fc6f24   0x00000038   Data   RO         1946    .constdata          app_security_task.o
    0x07fc6f5c   0x00000008   Data   RO         1975    .constdata          app_diss_task.o
    0x07fc6f64   0x0000008c   Data   RO         1991    .constdata          app_entry_point.o
    0x07fc6ff0   0x00000018   Data   RO         1992    .constdata          app_entry_point.o
    0x07fc7008   0x00000020   Data   RO         1993    .constdata          app_entry_point.o
    0x07fc7028   0x00000007   Data   RO         2103    .constdata          app_easy_security.o
    0x07fc702f   0x00000001   PAD
    0x07fc7030   0x00000038   Data   RO         2375    .constdata          user_custs_config.o
    0x07fc7068   0x00000010   Data   RO         2388    .constdata          user_custs1_def.o
    0x07fc7078   0x00000010   Data   RO         2389    .constdata          user_custs1_def.o
    0x07fc7088   0x00000010   Data   RO         2390    .constdata          user_custs1_def.o
    0x07fc7098   0x00000010   Data   RO         2391    .constdata          user_custs1_def.o
    0x07fc70a8   0x00000010   Data   RO         2392    .constdata          user_custs1_def.o
    0x07fc70b8   0x00000010   Data   RO         2393    .constdata          user_custs1_def.o
    0x07fc70c8   0x00000010   Data   RO         2394    .constdata          user_custs1_def.o
    0x07fc70d8   0x00000010   Data   RO         2395    .constdata          user_custs1_def.o
    0x07fc70e8   0x00000010   Data   RO         2396    .constdata          user_custs1_def.o
    0x07fc70f8   0x00000002   Data   RO         2397    .constdata          user_custs1_def.o
    0x07fc70fa   0x00000002   Data   RO         2398    .constdata          user_custs1_def.o
    0x07fc70fc   0x00000002   Data   RO         2399    .constdata          user_custs1_def.o
    0x07fc70fe   0x00000002   Data   RO         2400    .constdata          user_custs1_def.o
    0x07fc7100   0x00000005   Data   RO         2401    .constdata          user_custs1_def.o
    0x07fc7105   0x00000001   Data   RO         2402    .constdata          user_custs1_def.o
    0x07fc7106   0x00000002   PAD
    0x07fc7108   0x00000190   Data   RO         2404    .constdata          user_custs1_def.o
    0x07fc7298   0x00000008   Data   RO         2509    .constdata          user_custs1_impl.o
    0x07fc72a0   0x00000008   Data   RO         2704    .constdata          comm_task.o
    0x07fc72a8   0x00000008   Data   RO         2705    .constdata          comm_task.o
    0x07fc72b0   0x00000010   Data   RO         2706    .constdata          comm_task.o
    0x07fc72c0   0x00000009   Data   RO         2819    .constdata          comm_manager.o
    0x07fc72c9   0x00000003   PAD
    0x07fc72cc   0x000000dc   Data   RO         2879    .constdata          primitivemanager.o
    0x07fc73a8   0x00000068   Data   RO         2928    .constdata          transportmanager.o
    0x07fc7410   0x0000000c   Data   RO         2405    .conststring        user_custs1_def.o
    0x07fc741c   0x00000000   Data   RO         3271    Region$$Table       anon$$obj.o
    0x07fc741c   0x00000001   Data   RW         1488    .data               rf_531.o
    0x07fc741d   0x00000003   PAD
    0x07fc7420   0x00000030   Data   RW         2424    .data               user_periph_setup.o
    0x07fc7450   0x00000002   Data   RW         2587    .data               user_peripheral.o
    0x07fc7452   0x00000002   PAD
    0x07fc7454   0x00000018   Data   RW         2749    .data               serialinterface.o
    0x07fc746c   0x00000039   Data   RW         2880    .data               primitivemanager.o
    0x07fc74a5   0x00000012   Data   RW         2905    .data               primitivequeue.o
    0x07fc74b7   0x00000002   Data   RW         2929    .data               transportmanager.o
    0x07fc74b9   0x000002bc   Data   RW         2511    text                user_custs1_impl.o


    Execution Region ER_PRODTEST (Base: 0x07fc7778, Size: 0x00000000, Max: 0xffffffff, ABSOLUTE, UNINIT)

    **** No section assigned to this execution region ****


    Execution Region ER_ZI (Base: 0x07fc7778, Size: 0x00000e68, Max: 0xffffffff, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc7778   0x0000000c   Zero   RW          710    .bss                arch_system.o
    0x07fc7784   0x00000004   Zero   RW          916    .bss                otp_cs.o
    0x07fc7788   0x00000002   Zero   RW          966    .bss                syscntl.o
    0x07fc778a   0x00000002   PAD
    0x07fc778c   0x00000428   Zero   RW         1149    .bss                uart.o
    0x07fc7bb4   0x00000014   Zero   RW         1248    .bss                trng.o
    0x07fc7bc8   0x00000002   Zero   RW         1283    .bss                adc_531.o
    0x07fc7bca   0x00000002   PAD
    0x07fc7bcc   0x00000004   Zero   RW         1413    .bss                rwip.o
    0x07fc7bd0   0x0000002e   Zero   RW         1487    .bss                rf_531.o
    0x07fc7bfe   0x00000004   Zero   RW         2504    .bss                user_custs1_impl.o
    0x07fc7c02   0x00000006   Zero   RW         2585    .bss                user_peripheral.o
    0x07fc7c08   0x0000000a   Zero   RW         2703    .bss                comm_task.o
    0x07fc7c12   0x00000002   PAD
    0x07fc7c14   0x00000008   Zero   RW         2798    .bss                upperlayerinterface.o
    0x07fc7c1c   0x00000024   Zero   RW         2878    .bss                primitivemanager.o
    0x07fc7c40   0x000000d4   Zero   RW         2904    .bss                primitivequeue.o
    0x07fc7d14   0x000002b0   Zero   RW         2927    .bss                transportmanager.o
    0x07fc7fc4   0x0000001a   Zero   RW         2950    .bss                transportqueue.o
    0x07fc7fde   0x00000002   PAD
    0x07fc7fe0   0x00000600   Zero   RW          278    STACK               startup_da14531.o


    Execution Region ER_NZI (Base: 0x07fc85e0, Size: 0x0000040c, Max: 0xffffffff, ABSOLUTE, UNINIT)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc85e0   0x0000040c   Zero   RW          583    heap_mem_area_not_ret  jump_table.o


    Execution Region ER_STATEFUL_HIBERNATION (Base: 0x07fc89ec, Size: 0x00000000, Max: 0xffffffff, ABSOLUTE, UNINIT)

    **** No section assigned to this execution region ****



  Load Region LR_RETAINED_RAM0 (Base: 0x07fc8bc0, Size: 0x00000000, Max: 0x00001440, ABSOLUTE)

    Execution Region RET_DATA_UNINIT (Base: 0x07fc8bc0, Size: 0x00000000, Max: 0x00000000, ABSOLUTE, UNINIT)

    **** No section assigned to this execution region ****


    Execution Region RET_DATA (Base: 0x07fc8bc0, Size: 0x00000320, Max: 0x00000800, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc8bc0   0x00000008   Zero   RW          378    retention_mem_area0  arch_main.o
    0x07fc8bc8   0x00000004   Zero   RW          659    retention_mem_area0  arch_sleep.o
    0x07fc8bcc   0x00000004   PAD
    0x07fc8bd0   0x00000030   Zero   RW          714    retention_mem_area0  arch_system.o
    0x07fc8c00   0x00000002   Zero   RW          875    retention_mem_area0  arch_rom.o
    0x07fc8c02   0x00000002   PAD
    0x07fc8c04   0x0000005c   Zero   RW          918    retention_mem_area0  otp_cs.o
    0x07fc8c60   0x00000014   Zero   RW         1011    retention_mem_area0  gpio.o
    0x07fc8c74   0x00000001   Zero   RW         1086    retention_mem_area0  hw_otpc_531.o
    0x07fc8c75   0x00000003   PAD
    0x07fc8c78   0x00000004   Zero   RW         1284    retention_mem_area0  adc_531.o
    0x07fc8c7c   0x00000008   Zero   RW         1365    retention_mem_area0  rwble.o
    0x07fc8c84   0x00000004   Zero   RW         1415    retention_mem_area0  rwip.o
    0x07fc8c88   0x00000004   Zero   RW         1416    retention_mem_area0  rwip.o
    0x07fc8c8c   0x00000008   Zero   RW         1489    retention_mem_area0  rf_531.o
    0x07fc8c94   0x00000030   Zero   RW         1651    retention_mem_area0  prf.o
    0x07fc8cc4   0x0000004e   Zero   RW         1761    retention_mem_area0  app.o
    0x07fc8d12   0x00000014   Zero   RW         1762    retention_mem_area0  app.o
    0x07fc8d26   0x00000001   Zero   RW         1883    retention_mem_area0  app_task.o
    0x07fc8d27   0x00000001   PAD
    0x07fc8d28   0x0000007a   Zero   RW         1915    retention_mem_area0  app_security.o
    0x07fc8da2   0x00000002   PAD
    0x07fc8da4   0x00000018   Zero   RW         2105    retention_mem_area0  app_easy_security.o
    0x07fc8dbc   0x00000050   Zero   RW         2208    retention_mem_area0  app_easy_timer.o
    0x07fc8e0c   0x00000087   Zero   RW         2283    retention_mem_area0  app_bond_db.o
    0x07fc8e93   0x0000004a   Zero   RW         2588    retention_mem_area0  user_peripheral.o
    0x07fc8edd   0x00000001   Zero   RW         2707    retention_mem_area0  comm_task.o
    0x07fc8ede   0x00000002   Zero   RW         2708    retention_mem_area0  comm_task.o


    Execution Region RET_HEAP (Base: 0x07fc8ee0, Size: 0x00000c40, Max: 0x00000c40, ABSOLUTE, UNINIT)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fc8ee0   0x0000040c   Zero   RW          581    heap_db_area        jump_table.o
    0x07fc92ec   0x000002b8   Zero   RW          582    heap_env_area       jump_table.o
    0x07fc95a4   0x0000057c   Zero   RW          584    heap_msg_area       jump_table.o



  Load Region LR_FREE_AREA (Base: 0x07fcb4ac, Size: 0x00000000, Max: 0x000003f0, ABSOLUTE)

    Execution Region ER_FREE_AREA (Base: 0x07fcb4ac, Size: 0x000000a0, Max: 0x000003f0, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fcb4ac   0x000000a0   Zero   RW         2510    free_area           user_custs1_impl.o



  Load Region LR_RETAINED_TRNG_STATE (Base: 0x07fcb89c, Size: 0x00000000, Max: 0x00000004, ABSOLUTE)

    Execution Region RET_DATA_UNINIT_TRNG_STATE (Base: 0x07fcb89c, Size: 0x00000004, Max: 0x00000004, ABSOLUTE, UNINIT)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x07fcb89c   0x00000004   Zero   RW         1249    trng_state          trng.o

