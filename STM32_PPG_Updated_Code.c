// STM32 side - Updated PPG data transmission to BLE module
// This code should replace your existing PPG transmission

#include "main.h"
#include "string.h"

// External UART handles (adjust these to match your project)
extern UART_HandleTypeDef huart1;  // UART to BLE module (230.4k baud)
extern UART_HandleTypeDef huart3;  // Debug UART (optional)

// Your existing PPG data structure
extern struct {
    struct {
        uint32_t red;
        uint32_t ir;
    } ppg_data_in;
} algohub_in;

// Function to send PPG data to BLE module
void send_ppg_data_to_ble(void)
{
    // Simple 12-byte packet: [Header][RED][IR][Checksum]
    uint8_t ppg_packet[12];

    // Header
    ppg_packet[0] = 0xAA;  // Start marker
    ppg_packet[1] = 0x55;  // Start marker

    // RED value (4 bytes, little endian)
    uint32_t red = algohub_in.ppg_data_in.red;
    ppg_packet[2] = red & 0xFF;
    ppg_packet[3] = (red >> 8) & 0xFF;
    ppg_packet[4] = (red >> 16) & 0xFF;
    ppg_packet[5] = (red >> 24) & 0xFF;

    // IR value (4 bytes, little endian)
    uint32_t ir = algohub_in.ppg_data_in.ir;
    ppg_packet[6] = ir & 0xFF;
    ppg_packet[7] = (ir >> 8) & 0xFF;
    ppg_packet[8] = (ir >> 16) & 0xFF;
    ppg_packet[9] = (ir >> 24) & 0xFF;

    // Simple checksum
    uint8_t checksum = 0;
    for(int i = 2; i < 10; i++) {
        checksum ^= ppg_packet[i];
    }
    ppg_packet[10] = checksum;
    ppg_packet[11] = 0xFF;  // End marker

    // Send to BLE module via UART1 (connected to BLE module)
    HAL_UART_Transmit(&huart1, ppg_packet, 12, HAL_MAX_DELAY);

    // Optional: Keep UART3 debug output
    #ifdef DEBUG_PPG_UART3
    char uartBuf[64];
    int len = snprintf(uartBuf, sizeof(uartBuf),
                       "RED:%lu, IR:%lu\r\n",
                       red, ir);
    HAL_UART_Transmit(&huart3, (uint8_t*)uartBuf, len, HAL_MAX_DELAY);
    #endif
}

// Example of how to integrate this into your existing code
// Replace your existing PPG processing loop with this approach:

/*
// In your main PPG processing function, replace:
// 
// char uartBuf[64];
// int len = snprintf(uartBuf, sizeof(uartBuf),
//                    "RED:%lu, IR:%lu\r\n",
//                    algohub_in.ppg_data_in.red,
//                    algohub_in.ppg_data_in.ir);
// HAL_UART_Transmit(&huart3, (uint8_t*)uartBuf, len, HAL_MAX_DELAY);
//
// With:
send_ppg_data_to_ble();
*/

// Example integration in your main loop:
void process_ppg_data_example(void)
{
    // Your existing ADC processing code here...
    for(int i = 0; i < samples_per_iteration; i++) {
        algohub_in.ppg_data_in.red = adcCountArr[IX_PPG][2 + (i*6)];
        algohub_in.ppg_data_in.ir  = adcCountArr[IX_PPG][4 + (i*6)];
        
        // Send PPG data to BLE module instead of UART3
        send_ppg_data_to_ble();
        
        // Add small delay to prevent overwhelming the BLE module
        // Adjust this based on your sampling rate and BLE connection interval
        HAL_Delay(10); // 10ms delay - adjust as needed
    }
}

// Alternative: If you want to send data at a specific rate
void send_ppg_at_rate(void)
{
    static uint32_t last_send_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // Send PPG data every 50ms (20 Hz)
    if (current_time - last_send_time >= 50) {
        send_ppg_data_to_ble();
        last_send_time = current_time;
    }
}
