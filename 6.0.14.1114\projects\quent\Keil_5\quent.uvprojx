<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>DA14585</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060061::V5.06 update 1 (build 61)::ARMCC</pCCUsed>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM0</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.4.5.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IROM(0x0,0x0) IRAM(0x0,0x0) CPUTYPE("Cortex-M0") CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0NEW_DEVICE -FS00 -FL040000 -FP0($$Device:ARMCM0$Device\ARM\Flash\NEW_DEVICE.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM0$Device\ARM\ARMCM0\Include\ARMCM0.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:ARMCM0$Device\ARM\SVD\ARMCM0.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\out_DA14585\Objects\</OutputDirectory>
          <OutputName>quent_585</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\out_DA14585\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf --bincombined --output=".\out_DA14585\Objects\@L.bin"  "!L"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>1</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>0</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>0</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>0</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>6</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile>.\..\..\..\sdk\common_project_files\misc\sysram_case23.ini</InitializationFile>
            <Driver>Segger\JL2CM3.dll</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>1</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>0</AdsALst>
            <AdsACrf>0</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M0"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>0</StupSel>
            <useUlib>1</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>0</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>0</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <useXO>0</useXO>
            <v6Lang>0</v6Lang>
            <v6LangP>0</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <VariousControls>
              <MiscControls>--thumb -c --preinclude da1458x_config_basic.h --preinclude da1458x_config_advanced.h --preinclude user_config.h --bss_threshold=0 --feedback=".\unused_585.txt"</MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\sdk\app_modules\api;..\..\..\sdk\ble_stack\controller\em;..\..\..\sdk\ble_stack\controller\llc;..\..\..\sdk\ble_stack\controller\lld;..\..\..\sdk\ble_stack\controller\llm;..\..\..\sdk\ble_stack\ea\api;..\..\..\sdk\ble_stack\em\api;..\..\..\sdk\ble_stack\hci\api;..\..\..\sdk\ble_stack\hci\src;..\..\..\sdk\ble_stack\host\att;..\..\..\sdk\ble_stack\host\att\attc;..\..\..\sdk\ble_stack\host\att\attm;..\..\..\sdk\ble_stack\host\att\atts;..\..\..\sdk\ble_stack\host\gap;..\..\..\sdk\ble_stack\host\gap\gapc;..\..\..\sdk\ble_stack\host\gap\gapm;..\..\..\sdk\ble_stack\host\gatt;..\..\..\sdk\ble_stack\host\gatt\gattc;..\..\..\sdk\ble_stack\host\gatt\gattm;..\..\..\sdk\ble_stack\host\l2c\l2cc;..\..\..\sdk\ble_stack\host\l2c\l2cm;..\..\..\sdk\ble_stack\host\smp;..\..\..\sdk\ble_stack\host\smp\smpc;..\..\..\sdk\ble_stack\host\smp\smpm;..\..\..\sdk\ble_stack\profiles;..\..\..\sdk\ble_stack\profiles\anc;..\..\..\sdk\ble_stack\profiles\anc\ancc\api;..\..\..\sdk\ble_stack\profiles\anp;..\..\..\sdk\ble_stack\profiles\anp\anpc\api;..\..\..\sdk\ble_stack\profiles\anp\anps\api;..\..\..\sdk\ble_stack\profiles\bas\basc\api;..\..\..\sdk\ble_stack\profiles\bas\bass\api;..\..\..\sdk\ble_stack\profiles\bcs;..\..\..\sdk\ble_stack\profiles\bcs\bcsc\api;..\..\..\sdk\ble_stack\profiles\bcs\bcss\api;..\..\..\sdk\ble_stack\profiles\blp;..\..\..\sdk\ble_stack\profiles\blp\blpc\api;..\..\..\sdk\ble_stack\profiles\blp\blps\api;..\..\..\sdk\ble_stack\profiles\bms;..\..\..\sdk\ble_stack\profiles\bms\bmsc\api;..\..\..\sdk\ble_stack\profiles\bms\bmss\api;..\..\..\sdk\ble_stack\profiles\cpp;..\..\..\sdk\ble_stack\profiles\cpp\cppc\api;..\..\..\sdk\ble_stack\profiles\cpp\cpps\api;..\..\..\sdk\ble_stack\profiles\cscp;..\..\..\sdk\ble_stack\profiles\cscp\cscpc\api;..\..\..\sdk\ble_stack\profiles\cscp\cscps\api;..\..\..\sdk\ble_stack\profiles\cts;..\..\..\sdk\ble_stack\profiles\cts\ctsc\api;..\..\..\sdk\ble_stack\profiles\cts\ctss\api;..\..\..\sdk\ble_stack\profiles\custom;..\..\..\sdk\ble_stack\profiles\custom\custs\api;..\..\..\sdk\ble_stack\profiles\dis\disc\api;..\..\..\sdk\ble_stack\profiles\dis\diss\api;..\..\..\sdk\ble_stack\profiles\find;..\..\..\sdk\ble_stack\profiles\find\findl\api;..\..\..\sdk\ble_stack\profiles\find\findt\api;..\..\..\sdk\ble_stack\profiles\gatt\gatt_client\api;..\..\..\sdk\ble_stack\profiles\glp;..\..\..\sdk\ble_stack\profiles\glp\glpc\api;..\..\..\sdk\ble_stack\profiles\glp\glps\api;..\..\..\sdk\ble_stack\profiles\hogp;..\..\..\sdk\ble_stack\profiles\hogp\hogpbh\api;..\..\..\sdk\ble_stack\profiles\hogp\hogpd\api;..\..\..\sdk\ble_stack\profiles\hogp\hogprh\api;..\..\..\sdk\ble_stack\profiles\hrp;..\..\..\sdk\ble_stack\profiles\hrp\hrpc\api;..\..\..\sdk\ble_stack\profiles\hrp\hrps\api;..\..\..\sdk\ble_stack\profiles\htp;..\..\..\sdk\ble_stack\profiles\htp\htpc\api;..\..\..\sdk\ble_stack\profiles\htp\htpt\api;..\..\..\sdk\ble_stack\profiles\lan;..\..\..\sdk\ble_stack\profiles\lan\lanc\api;..\..\..\sdk\ble_stack\profiles\lan\lans\api;..\..\..\sdk\ble_stack\profiles\pasp;..\..\..\sdk\ble_stack\profiles\pasp\paspc\api;..\..\..\sdk\ble_stack\profiles\pasp\pasps\api;..\..\..\sdk\ble_stack\profiles\prox\proxm\api;..\..\..\sdk\ble_stack\profiles\prox\proxr\api;..\..\..\sdk\ble_stack\profiles\rscp;..\..\..\sdk\ble_stack\profiles\rscp\rscpc\api;..\..\..\sdk\ble_stack\profiles\rscp\rscps\api;..\..\..\sdk\ble_stack\profiles\scpp;..\..\..\sdk\ble_stack\profiles\scpp\scppc\api;..\..\..\sdk\ble_stack\profiles\scpp\scpps\api;..\..\..\sdk\ble_stack\profiles\suota\suotar\api;..\..\..\sdk\ble_stack\profiles\tip;..\..\..\sdk\ble_stack\profiles\tip\tipc\api;..\..\..\sdk\ble_stack\profiles\tip\tips\api;..\..\..\sdk\ble_stack\profiles\uds;..\..\..\sdk\ble_stack\profiles\uds\udsc\api;..\..\..\sdk\ble_stack\profiles\uds\udss\api;..\..\..\sdk\ble_stack\profiles\wss;..\..\..\sdk\ble_stack\profiles\wss\wssc\api;..\..\..\sdk\ble_stack\profiles\wss\wsss\api;..\..\..\sdk\ble_stack\rwble;..\..\..\sdk\ble_stack\rwble_hl;..\..\..\sdk\common_project_files;..\..\..\sdk\platform\arch;..\..\..\sdk\platform\arch\boot;..\..\..\sdk\platform\arch\boot\ARM;..\..\..\sdk\platform\arch\boot\GCC;..\..\..\sdk\platform\arch\compiler;..\..\..\sdk\platform\arch\compiler\ARM;..\..\..\sdk\platform\arch\compiler\GCC;..\..\..\sdk\platform\arch\ll;..\..\..\sdk\platform\arch\main;..\..\..\sdk\platform\core_modules\arch_console;..\..\..\sdk\platform\core_modules\common\api;..\..\..\sdk\platform\core_modules\crypto;..\..\..\sdk\platform\core_modules\dbg\api;..\..\..\sdk\platform\core_modules\gtl\api;..\..\..\sdk\platform\core_modules\gtl\src;..\..\..\sdk\platform\core_modules\h4tl\api;..\..\..\sdk\platform\core_modules\ke\api;..\..\..\sdk\platform\core_modules\ke\src;..\..\..\sdk\platform\core_modules\nvds\api;..\..\..\sdk\platform\core_modules\rf\api;..\..\..\sdk\platform\core_modules\rwip\api;..\..\..\sdk\platform\driver\adc;..\..\..\sdk\platform\driver\battery;..\..\..\sdk\platform\driver\ble;..\..\..\sdk\platform\driver\dma;..\..\..\sdk\platform\driver\gpio;..\..\..\sdk\platform\driver\hw_otpc;..\..\..\sdk\platform\driver\i2c;..\..\..\sdk\platform\driver\i2c_eeprom;..\..\..\sdk\platform\driver\pdm;..\..\..\sdk\platform\driver\reg;..\..\..\sdk\platform\driver\rtc;..\..\..\sdk\platform\driver\spi;..\..\..\sdk\platform\driver\spi_flash;..\..\..\sdk\platform\driver\spi_hci;..\..\..\sdk\platform\driver\syscntl;..\..\..\sdk\platform\driver\systick;..\..\..\sdk\platform\driver\timer;..\..\..\sdk\platform\driver\trng;..\..\..\sdk\platform\driver\uart;..\..\..\sdk\platform\driver\wkupct_quadec;..\..\..\sdk\platform\include;..\..\..\sdk\platform\system_library\include;..\..\..\third_party\hash;..\..\..\third_party\rand;.\..\src;.\..\src\config;.\..\src\custom_profile;..\..\..\sdk\platform\utilities\otp_hdr;..\..\..\sdk\platform\include\CMSIS\5.6.0\Include</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>1</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x00000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>..\..\..\sdk\common_project_files\scatterfiles\DA14585_586.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--feedback=".\unused_585.txt" ..\..\..\sdk\common_project_files\misc\da14585_symbols.txt --symdefs=quent_585_symdef.txt --any_placement=best_fit --datacompressor off</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>sdk_boot</GroupName>
          <Files>
            <File>
              <FileName>system_DA14585_586.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\boot\system_DA14585_586.c</FilePath>
            </File>
            <File>
              <FileName>startup_DA14585_586.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\sdk\platform\arch\boot\ARM\startup_DA14585_586.s</FilePath>
            </File>
            <File>
              <FileName>system_DA14531.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\boot\system_DA14531.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>0</vShortEn>
                    <vShortWch>0</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>startup_DA14531.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\sdk\platform\arch\boot\ARM\startup_DA14531.s</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Aads>
                    <interw>2</interw>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <thumb>2</thumb>
                    <SplitLS>2</SplitLS>
                    <SwStkChk>2</SwStkChk>
                    <NoWarn>2</NoWarn>
                    <uSurpInc>2</uSurpInc>
                    <useXO>2</useXO>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Aads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>hardfault_handler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\hardfault_handler.c</FilePath>
            </File>
            <File>
              <FileName>nmi_handler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\nmi_handler.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_arch</GroupName>
          <Files>
            <File>
              <FileName>arch_console.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\arch_console\arch_console.c</FilePath>
            </File>
            <File>
              <FileName>nvds.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\nvds\src\nvds.c</FilePath>
            </File>
            <File>
              <FileName>arch_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_main.c</FilePath>
            </File>
            <File>
              <FileName>jump_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\jump_table.c</FilePath>
            </File>
            <File>
              <FileName>arch_sleep.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_sleep.c</FilePath>
            </File>
            <File>
              <FileName>arch_system.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_system.c</FilePath>
            </File>
            <File>
              <FileName>arch_hibernation.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_hibernation.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>0</AlwaysBuild>
                  <GenerateAssemblyFile>0</GenerateAssemblyFile>
                  <AssembleAssemblyFile>0</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>0</vShortEn>
                    <vShortWch>0</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>arch_rom.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_rom.c</FilePath>
            </File>
            <File>
              <FileName>chacha20.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\third_party\rand\chacha20.c</FilePath>
            </File>
            <File>
              <FileName>hash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\third_party\hash\hash.c</FilePath>
            </File>
            <File>
              <FileName>da14585_586.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\sdk\platform\system_library\output\Keil_5\da14585_586.lib</FilePath>
            </File>
            <File>
              <FileName>da14531.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\sdk\platform\system_library\output\Keil_5\da14531.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>otp_cs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\utilities\otp_cs\otp_cs.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>0</AlwaysBuild>
                  <GenerateAssemblyFile>0</GenerateAssemblyFile>
                  <AssembleAssemblyFile>0</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>0</vShortEn>
                    <vShortWch>0</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>otp_hdr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\utilities\otp_hdr\otp_hdr.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_driver</GroupName>
          <Files>
            <File>
              <FileName>syscntl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\syscntl\syscntl.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\gpio\gpio.c</FilePath>
            </File>
            <File>
              <FileName>hw_otpc_58x.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\hw_otpc\hw_otpc_58x.c</FilePath>
            </File>
            <File>
              <FileName>hw_otpc_531.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\hw_otpc\hw_otpc_531.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>0</AlwaysBuild>
                  <GenerateAssemblyFile>0</GenerateAssemblyFile>
                  <AssembleAssemblyFile>0</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>0</vShortEn>
                    <vShortWch>0</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\uart\uart.c</FilePath>
            </File>
            <File>
              <FileName>trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\trng\trng.c</FilePath>
            </File>
            <File>
              <FileName>dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\dma\dma.c</FilePath>
            </File>
            <File>
              <FileName>wlan_coex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\wifi\wlan_coex.c</FilePath>
            </File>
            <File>
              <FileName>adc_531.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\adc\adc_531.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_ble</GroupName>
          <Files>
            <File>
              <FileName>rwble.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\rwble\rwble.c</FilePath>
            </File>
            <File>
              <FileName>rwip.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\rwip\src\rwip.c</FilePath>
            </File>
            <File>
              <FileName>rf_585.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\rf\src\rf_585.c</FilePath>
            </File>
            <File>
              <FileName>ble_arp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\rf\src\ble_arp.c</FilePath>
            </File>
            <File>
              <FileName>rf_531.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\rf\src\rf_531.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_profiles</GroupName>
          <Files>
            <File>
              <FileName>attm_db_128.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\host\att\attm\attm_db_128.c</FilePath>
            </File>
            <File>
              <FileName>prf_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\prf_utils.c</FilePath>
            </File>
            <File>
              <FileName>custom_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\custom\custom_common.c</FilePath>
            </File>
            <File>
              <FileName>diss.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\dis\diss\src\diss.c</FilePath>
            </File>
            <File>
              <FileName>diss_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\dis\diss\src\diss_task.c</FilePath>
            </File>
            <File>
              <FileName>custs1.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\custom\custs\src\custs1.c</FilePath>
            </File>
            <File>
              <FileName>custs1_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\custom\custs\src\custs1_task.c</FilePath>
            </File>
            <File>
              <FileName>prf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\prf.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_app</GroupName>
          <Files>
            <File>
              <FileName>app_default_handlers.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_default_hnd\app_default_handlers.c</FilePath>
            </File>
            <File>
              <FileName>app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_common\app.c</FilePath>
            </File>
            <File>
              <FileName>app_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_common\app_task.c</FilePath>
            </File>
            <File>
              <FileName>app_security.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_sec\app_security.c</FilePath>
            </File>
            <File>
              <FileName>app_security_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_sec\app_security_task.c</FilePath>
            </File>
            <File>
              <FileName>app_bass.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_bass\app_bass.c</FilePath>
            </File>
            <File>
              <FileName>app_bass_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_bass\app_bass_task.c</FilePath>
            </File>
            <File>
              <FileName>app_findme.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_findme\app_findme.c</FilePath>
            </File>
            <File>
              <FileName>app_findme_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_findme\app_findme_task.c</FilePath>
            </File>
            <File>
              <FileName>app_proxr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_proxr\app_proxr.c</FilePath>
            </File>
            <File>
              <FileName>app_proxr_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_proxr\app_proxr_task.c</FilePath>
            </File>
            <File>
              <FileName>app_diss.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_diss\app_diss.c</FilePath>
            </File>
            <File>
              <FileName>app_diss_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_diss\app_diss_task.c</FilePath>
            </File>
            <File>
              <FileName>app_suotar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_suotar\app_suotar.c</FilePath>
            </File>
            <File>
              <FileName>app_suotar_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_suotar\app_suotar_task.c</FilePath>
            </File>
            <File>
              <FileName>app_entry_point.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_entry\app_entry_point.c</FilePath>
            </File>
            <File>
              <FileName>app_msg_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_common\app_msg_utils.c</FilePath>
            </File>
            <File>
              <FileName>app_easy_msg_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_easy\app_easy_msg_utils.c</FilePath>
            </File>
            <File>
              <FileName>app_easy_security.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_easy\app_easy_security.c</FilePath>
            </File>
            <File>
              <FileName>app_easy_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_easy\app_easy_timer.c</FilePath>
            </File>
            <File>
              <FileName>app_customs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_custs\app_customs.c</FilePath>
            </File>
            <File>
              <FileName>app_customs_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_custs\app_customs_task.c</FilePath>
            </File>
            <File>
              <FileName>app_customs_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_custs\app_customs_common.c</FilePath>
            </File>
            <File>
              <FileName>app_bond_db.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_bond_db\app_bond_db.c</FilePath>
            </File>
            <File>
              <FileName>app_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_common\app_utils.c</FilePath>
            </File>
            <File>
              <FileName>app_easy_whitelist.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_easy\app_easy_whitelist.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_config</GroupName>
          <Files>
            <File>
              <FileName>da1458x_config_advanced.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\da1458x_config_advanced.h</FilePath>
            </File>
            <File>
              <FileName>da1458x_config_basic.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\da1458x_config_basic.h</FilePath>
            </File>
            <File>
              <FileName>user_callback_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_callback_config.h</FilePath>
            </File>
            <File>
              <FileName>user_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_config.h</FilePath>
            </File>
            <File>
              <FileName>user_modules_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_modules_config.h</FilePath>
            </File>
            <File>
              <FileName>user_periph_setup.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_periph_setup.h</FilePath>
            </File>
            <File>
              <FileName>user_profiles_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_profiles_config.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_custom_profile</GroupName>
          <Files>
            <File>
              <FileName>user_custs_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\custom_profile\user_custs_config.c</FilePath>
            </File>
            <File>
              <FileName>user_custs1_def.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\custom_profile\user_custs1_def.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_platform</GroupName>
          <Files>
            <File>
              <FileName>user_periph_setup.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\platform\user_periph_setup.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_app</GroupName>
          <Files>
            <File>
              <FileName>user_custs1_impl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\user_custs1_impl.c</FilePath>
            </File>
            <File>
              <FileName>user_peripheral.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\user_peripheral.c</FilePath>
            </File>
            <File>
              <FileName>scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\scheduler.c</FilePath>
            </File>
            <File>
              <FileName>ring_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\custom_profile\ring_buf.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>comm_manager</GroupName>
          <Files>
            <File>
              <FileName>comm_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\comm_task.c</FilePath>
            </File>
            <File>
              <FileName>SerialInterface.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\SerialInterface.c</FilePath>
            </File>
            <File>
              <FileName>comm_task.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\comm_task.h</FilePath>
            </File>
            <File>
              <FileName>Comm_Debug.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\Comm_Debug.h</FilePath>
            </File>
            <File>
              <FileName>UpperLayerInterface.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\UpperLayerInterface.h</FilePath>
            </File>
            <File>
              <FileName>SerialInterface.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\SerialInterface.h</FilePath>
            </File>
            <File>
              <FileName>UpperLayerInterface.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\UpperLayerInterface.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>comm_manager_lib</GroupName>
          <Files>
            <File>
              <FileName>Comm_Manager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\Comm_Manager.c</FilePath>
            </File>
            <File>
              <FileName>Comm_Manager.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\Comm_Manager.h</FilePath>
            </File>
            <File>
              <FileName>PrimitiveManager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\PrimitiveManager.c</FilePath>
            </File>
            <File>
              <FileName>PrimitiveQueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\PrimitiveQueue.c</FilePath>
            </File>
            <File>
              <FileName>PrimitiveQueue.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\PrimitiveQueue.h</FilePath>
            </File>
            <File>
              <FileName>TransportManager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\TransportManager.c</FilePath>
            </File>
            <File>
              <FileName>TransportQueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\TransportQueue.c</FilePath>
            </File>
            <File>
              <FileName>TransportQueue.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\TransportQueue.h</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>DA14586</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM0</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.4.5.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IROM(0x0,0x0) IRAM(0x0,0x0) CPUTYPE("Cortex-M0") CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0NEW_DEVICE -FS00 -FL040000 -FP0($$Device:ARMCM0$Device\ARM\Flash\NEW_DEVICE.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM0$Device\ARM\ARMCM0\Include\ARMCM0.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:ARMCM0$Device\ARM\SVD\ARMCM0.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\out_DA14586\Objects\</OutputDirectory>
          <OutputName>quent_586</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\out_DA14586\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf --bincombined --output=".\out_DA14586\Objects\@L.bin"  "!L"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>1</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>0</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>0</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>0</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>4</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile>.\..\..\..\sdk\common_project_files\misc\sysram_case23.ini</InitializationFile>
            <Driver>Segger\JL2CM3.dll</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>0</UseTargetDll>
            <UseExternalTool>1</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>0</AdsALst>
            <AdsACrf>0</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M0"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>0</StupSel>
            <useUlib>1</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>0</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>0</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <useXO>0</useXO>
            <v6Lang>0</v6Lang>
            <v6LangP>0</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <VariousControls>
              <MiscControls>--c99 --thumb -c --preinclude da1458x_config_basic.h --preinclude da1458x_config_advanced.h --preinclude user_config.h --bss_threshold=0 --feedback=".\unused_586.txt"</MiscControls>
              <Define>__DA14586__</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\sdk\app_modules\api;..\..\..\sdk\ble_stack\controller\em;..\..\..\sdk\ble_stack\controller\llc;..\..\..\sdk\ble_stack\controller\lld;..\..\..\sdk\ble_stack\controller\llm;..\..\..\sdk\ble_stack\ea\api;..\..\..\sdk\ble_stack\em\api;..\..\..\sdk\ble_stack\hci\api;..\..\..\sdk\ble_stack\hci\src;..\..\..\sdk\ble_stack\host\att;..\..\..\sdk\ble_stack\host\att\attc;..\..\..\sdk\ble_stack\host\att\attm;..\..\..\sdk\ble_stack\host\att\atts;..\..\..\sdk\ble_stack\host\gap;..\..\..\sdk\ble_stack\host\gap\gapc;..\..\..\sdk\ble_stack\host\gap\gapm;..\..\..\sdk\ble_stack\host\gatt;..\..\..\sdk\ble_stack\host\gatt\gattc;..\..\..\sdk\ble_stack\host\gatt\gattm;..\..\..\sdk\ble_stack\host\l2c\l2cc;..\..\..\sdk\ble_stack\host\l2c\l2cm;..\..\..\sdk\ble_stack\host\smp;..\..\..\sdk\ble_stack\host\smp\smpc;..\..\..\sdk\ble_stack\host\smp\smpm;..\..\..\sdk\ble_stack\profiles;..\..\..\sdk\ble_stack\profiles\anc;..\..\..\sdk\ble_stack\profiles\anc\ancc\api;..\..\..\sdk\ble_stack\profiles\anp;..\..\..\sdk\ble_stack\profiles\anp\anpc\api;..\..\..\sdk\ble_stack\profiles\anp\anps\api;..\..\..\sdk\ble_stack\profiles\bas\basc\api;..\..\..\sdk\ble_stack\profiles\bas\bass\api;..\..\..\sdk\ble_stack\profiles\bcs;..\..\..\sdk\ble_stack\profiles\bcs\bcsc\api;..\..\..\sdk\ble_stack\profiles\bcs\bcss\api;..\..\..\sdk\ble_stack\profiles\blp;..\..\..\sdk\ble_stack\profiles\blp\blpc\api;..\..\..\sdk\ble_stack\profiles\blp\blps\api;..\..\..\sdk\ble_stack\profiles\bms;..\..\..\sdk\ble_stack\profiles\bms\bmsc\api;..\..\..\sdk\ble_stack\profiles\bms\bmss\api;..\..\..\sdk\ble_stack\profiles\cpp;..\..\..\sdk\ble_stack\profiles\cpp\cppc\api;..\..\..\sdk\ble_stack\profiles\cpp\cpps\api;..\..\..\sdk\ble_stack\profiles\cscp;..\..\..\sdk\ble_stack\profiles\cscp\cscpc\api;..\..\..\sdk\ble_stack\profiles\cscp\cscps\api;..\..\..\sdk\ble_stack\profiles\cts;..\..\..\sdk\ble_stack\profiles\cts\ctsc\api;..\..\..\sdk\ble_stack\profiles\cts\ctss\api;..\..\..\sdk\ble_stack\profiles\custom;..\..\..\sdk\ble_stack\profiles\custom\custs\api;..\..\..\sdk\ble_stack\profiles\dis\disc\api;..\..\..\sdk\ble_stack\profiles\dis\diss\api;..\..\..\sdk\ble_stack\profiles\find;..\..\..\sdk\ble_stack\profiles\find\findl\api;..\..\..\sdk\ble_stack\profiles\find\findt\api;..\..\..\sdk\ble_stack\profiles\gatt\gatt_client\api;..\..\..\sdk\ble_stack\profiles\glp;..\..\..\sdk\ble_stack\profiles\glp\glpc\api;..\..\..\sdk\ble_stack\profiles\glp\glps\api;..\..\..\sdk\ble_stack\profiles\hogp;..\..\..\sdk\ble_stack\profiles\hogp\hogpbh\api;..\..\..\sdk\ble_stack\profiles\hogp\hogpd\api;..\..\..\sdk\ble_stack\profiles\hogp\hogprh\api;..\..\..\sdk\ble_stack\profiles\hrp;..\..\..\sdk\ble_stack\profiles\hrp\hrpc\api;..\..\..\sdk\ble_stack\profiles\hrp\hrps\api;..\..\..\sdk\ble_stack\profiles\htp;..\..\..\sdk\ble_stack\profiles\htp\htpc\api;..\..\..\sdk\ble_stack\profiles\htp\htpt\api;..\..\..\sdk\ble_stack\profiles\lan;..\..\..\sdk\ble_stack\profiles\lan\lanc\api;..\..\..\sdk\ble_stack\profiles\lan\lans\api;..\..\..\sdk\ble_stack\profiles\pasp;..\..\..\sdk\ble_stack\profiles\pasp\paspc\api;..\..\..\sdk\ble_stack\profiles\pasp\pasps\api;..\..\..\sdk\ble_stack\profiles\prox\proxm\api;..\..\..\sdk\ble_stack\profiles\prox\proxr\api;..\..\..\sdk\ble_stack\profiles\rscp;..\..\..\sdk\ble_stack\profiles\rscp\rscpc\api;..\..\..\sdk\ble_stack\profiles\rscp\rscps\api;..\..\..\sdk\ble_stack\profiles\scpp;..\..\..\sdk\ble_stack\profiles\scpp\scppc\api;..\..\..\sdk\ble_stack\profiles\scpp\scpps\api;..\..\..\sdk\ble_stack\profiles\suota\suotar\api;..\..\..\sdk\ble_stack\profiles\tip;..\..\..\sdk\ble_stack\profiles\tip\tipc\api;..\..\..\sdk\ble_stack\profiles\tip\tips\api;..\..\..\sdk\ble_stack\profiles\uds;..\..\..\sdk\ble_stack\profiles\uds\udsc\api;..\..\..\sdk\ble_stack\profiles\uds\udss\api;..\..\..\sdk\ble_stack\profiles\wss;..\..\..\sdk\ble_stack\profiles\wss\wssc\api;..\..\..\sdk\ble_stack\profiles\wss\wsss\api;..\..\..\sdk\ble_stack\rwble;..\..\..\sdk\ble_stack\rwble_hl;..\..\..\sdk\common_project_files;..\..\..\sdk\platform\arch;..\..\..\sdk\platform\arch\boot;..\..\..\sdk\platform\arch\boot\ARM;..\..\..\sdk\platform\arch\boot\GCC;..\..\..\sdk\platform\arch\compiler;..\..\..\sdk\platform\arch\compiler\ARM;..\..\..\sdk\platform\arch\compiler\GCC;..\..\..\sdk\platform\arch\ll;..\..\..\sdk\platform\arch\main;..\..\..\sdk\platform\core_modules\arch_console;..\..\..\sdk\platform\core_modules\common\api;..\..\..\sdk\platform\core_modules\crypto;..\..\..\sdk\platform\core_modules\dbg\api;..\..\..\sdk\platform\core_modules\gtl\api;..\..\..\sdk\platform\core_modules\gtl\src;..\..\..\sdk\platform\core_modules\h4tl\api;..\..\..\sdk\platform\core_modules\ke\api;..\..\..\sdk\platform\core_modules\ke\src;..\..\..\sdk\platform\core_modules\nvds\api;..\..\..\sdk\platform\core_modules\rf\api;..\..\..\sdk\platform\core_modules\rwip\api;..\..\..\sdk\platform\driver\adc;..\..\..\sdk\platform\driver\battery;..\..\..\sdk\platform\driver\ble;..\..\..\sdk\platform\driver\dma;..\..\..\sdk\platform\driver\gpio;..\..\..\sdk\platform\driver\hw_otpc;..\..\..\sdk\platform\driver\i2c;..\..\..\sdk\platform\driver\i2c_eeprom;..\..\..\sdk\platform\driver\pdm;..\..\..\sdk\platform\driver\reg;..\..\..\sdk\platform\driver\rtc;..\..\..\sdk\platform\driver\spi;..\..\..\sdk\platform\driver\spi_flash;..\..\..\sdk\platform\driver\spi_hci;..\..\..\sdk\platform\driver\syscntl;..\..\..\sdk\platform\driver\systick;..\..\..\sdk\platform\driver\timer;..\..\..\sdk\platform\driver\trng;..\..\..\sdk\platform\driver\uart;..\..\..\sdk\platform\driver\wkupct_quadec;..\..\..\sdk\platform\include;..\..\..\sdk\platform\system_library\include;..\..\..\third_party\hash;..\..\..\third_party\rand;.\..\src;.\..\src\config;.\..\src\custom_profile;..\..\..\sdk\platform\utilities\otp_hdr;..\..\..\sdk\platform\include\CMSIS\5.6.0\Include</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>1</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x00000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>..\..\..\sdk\common_project_files\scatterfiles\DA14585_586.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--feedback=".\unused_586.txt" ..\..\..\sdk\common_project_files\misc\da14585_symbols.txt --symdefs=quent_586_symdef.txt --any_placement=best_fit --datacompressor off</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>sdk_boot</GroupName>
          <Files>
            <File>
              <FileName>system_DA14585_586.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\boot\system_DA14585_586.c</FilePath>
            </File>
            <File>
              <FileName>startup_DA14585_586.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\sdk\platform\arch\boot\ARM\startup_DA14585_586.s</FilePath>
            </File>
            <File>
              <FileName>system_DA14531.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\boot\system_DA14531.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>0</vShortEn>
                    <vShortWch>0</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>startup_DA14531.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\sdk\platform\arch\boot\ARM\startup_DA14531.s</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Aads>
                    <interw>2</interw>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <thumb>2</thumb>
                    <SplitLS>2</SplitLS>
                    <SwStkChk>2</SwStkChk>
                    <NoWarn>2</NoWarn>
                    <uSurpInc>2</uSurpInc>
                    <useXO>2</useXO>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Aads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>hardfault_handler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\hardfault_handler.c</FilePath>
            </File>
            <File>
              <FileName>nmi_handler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\nmi_handler.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_arch</GroupName>
          <Files>
            <File>
              <FileName>arch_console.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\arch_console\arch_console.c</FilePath>
            </File>
            <File>
              <FileName>nvds.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\nvds\src\nvds.c</FilePath>
            </File>
            <File>
              <FileName>arch_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_main.c</FilePath>
            </File>
            <File>
              <FileName>jump_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\jump_table.c</FilePath>
            </File>
            <File>
              <FileName>arch_sleep.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_sleep.c</FilePath>
            </File>
            <File>
              <FileName>arch_system.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_system.c</FilePath>
            </File>
            <File>
              <FileName>arch_hibernation.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_hibernation.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>0</AlwaysBuild>
                  <GenerateAssemblyFile>0</GenerateAssemblyFile>
                  <AssembleAssemblyFile>0</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>0</vShortEn>
                    <vShortWch>0</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>arch_rom.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_rom.c</FilePath>
            </File>
            <File>
              <FileName>chacha20.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\third_party\rand\chacha20.c</FilePath>
            </File>
            <File>
              <FileName>hash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\third_party\hash\hash.c</FilePath>
            </File>
            <File>
              <FileName>da14585_586.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\sdk\platform\system_library\output\Keil_5\da14585_586.lib</FilePath>
            </File>
            <File>
              <FileName>da14531.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\sdk\platform\system_library\output\Keil_5\da14531.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>otp_cs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\utilities\otp_cs\otp_cs.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>0</AlwaysBuild>
                  <GenerateAssemblyFile>0</GenerateAssemblyFile>
                  <AssembleAssemblyFile>0</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>0</vShortEn>
                    <vShortWch>0</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>otp_hdr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\utilities\otp_hdr\otp_hdr.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_driver</GroupName>
          <Files>
            <File>
              <FileName>syscntl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\syscntl\syscntl.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\gpio\gpio.c</FilePath>
            </File>
            <File>
              <FileName>hw_otpc_58x.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\hw_otpc\hw_otpc_58x.c</FilePath>
            </File>
            <File>
              <FileName>hw_otpc_531.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\hw_otpc\hw_otpc_531.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>0</AlwaysBuild>
                  <GenerateAssemblyFile>0</GenerateAssemblyFile>
                  <AssembleAssemblyFile>0</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>0</vShortEn>
                    <vShortWch>0</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\uart\uart.c</FilePath>
            </File>
            <File>
              <FileName>trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\trng\trng.c</FilePath>
            </File>
            <File>
              <FileName>dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\dma\dma.c</FilePath>
            </File>
            <File>
              <FileName>wlan_coex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\wifi\wlan_coex.c</FilePath>
            </File>
            <File>
              <FileName>adc_531.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\adc\adc_531.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_ble</GroupName>
          <Files>
            <File>
              <FileName>rwble.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\rwble\rwble.c</FilePath>
            </File>
            <File>
              <FileName>rwip.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\rwip\src\rwip.c</FilePath>
            </File>
            <File>
              <FileName>rf_585.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\rf\src\rf_585.c</FilePath>
            </File>
            <File>
              <FileName>ble_arp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\rf\src\ble_arp.c</FilePath>
            </File>
            <File>
              <FileName>rf_531.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\rf\src\rf_531.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_profiles</GroupName>
          <Files>
            <File>
              <FileName>attm_db_128.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\host\att\attm\attm_db_128.c</FilePath>
            </File>
            <File>
              <FileName>prf_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\prf_utils.c</FilePath>
            </File>
            <File>
              <FileName>custom_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\custom\custom_common.c</FilePath>
            </File>
            <File>
              <FileName>diss.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\dis\diss\src\diss.c</FilePath>
            </File>
            <File>
              <FileName>diss_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\dis\diss\src\diss_task.c</FilePath>
            </File>
            <File>
              <FileName>custs1.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\custom\custs\src\custs1.c</FilePath>
            </File>
            <File>
              <FileName>custs1_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\custom\custs\src\custs1_task.c</FilePath>
            </File>
            <File>
              <FileName>prf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\prf.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_app</GroupName>
          <Files>
            <File>
              <FileName>app_default_handlers.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_default_hnd\app_default_handlers.c</FilePath>
            </File>
            <File>
              <FileName>app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_common\app.c</FilePath>
            </File>
            <File>
              <FileName>app_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_common\app_task.c</FilePath>
            </File>
            <File>
              <FileName>app_security.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_sec\app_security.c</FilePath>
            </File>
            <File>
              <FileName>app_security_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_sec\app_security_task.c</FilePath>
            </File>
            <File>
              <FileName>app_bass.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_bass\app_bass.c</FilePath>
            </File>
            <File>
              <FileName>app_bass_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_bass\app_bass_task.c</FilePath>
            </File>
            <File>
              <FileName>app_findme.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_findme\app_findme.c</FilePath>
            </File>
            <File>
              <FileName>app_findme_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_findme\app_findme_task.c</FilePath>
            </File>
            <File>
              <FileName>app_proxr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_proxr\app_proxr.c</FilePath>
            </File>
            <File>
              <FileName>app_proxr_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_proxr\app_proxr_task.c</FilePath>
            </File>
            <File>
              <FileName>app_diss.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_diss\app_diss.c</FilePath>
            </File>
            <File>
              <FileName>app_diss_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_diss\app_diss_task.c</FilePath>
            </File>
            <File>
              <FileName>app_suotar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_suotar\app_suotar.c</FilePath>
            </File>
            <File>
              <FileName>app_suotar_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_suotar\app_suotar_task.c</FilePath>
            </File>
            <File>
              <FileName>app_entry_point.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_entry\app_entry_point.c</FilePath>
            </File>
            <File>
              <FileName>app_msg_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_common\app_msg_utils.c</FilePath>
            </File>
            <File>
              <FileName>app_easy_msg_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_easy\app_easy_msg_utils.c</FilePath>
            </File>
            <File>
              <FileName>app_easy_security.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_easy\app_easy_security.c</FilePath>
            </File>
            <File>
              <FileName>app_easy_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_easy\app_easy_timer.c</FilePath>
            </File>
            <File>
              <FileName>app_customs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_custs\app_customs.c</FilePath>
            </File>
            <File>
              <FileName>app_customs_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_custs\app_customs_task.c</FilePath>
            </File>
            <File>
              <FileName>app_customs_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_custs\app_customs_common.c</FilePath>
            </File>
            <File>
              <FileName>app_bond_db.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_bond_db\app_bond_db.c</FilePath>
            </File>
            <File>
              <FileName>app_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_common\app_utils.c</FilePath>
            </File>
            <File>
              <FileName>app_easy_whitelist.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_easy\app_easy_whitelist.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_config</GroupName>
          <Files>
            <File>
              <FileName>da1458x_config_advanced.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\da1458x_config_advanced.h</FilePath>
            </File>
            <File>
              <FileName>da1458x_config_basic.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\da1458x_config_basic.h</FilePath>
            </File>
            <File>
              <FileName>user_callback_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_callback_config.h</FilePath>
            </File>
            <File>
              <FileName>user_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_config.h</FilePath>
            </File>
            <File>
              <FileName>user_modules_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_modules_config.h</FilePath>
            </File>
            <File>
              <FileName>user_periph_setup.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_periph_setup.h</FilePath>
            </File>
            <File>
              <FileName>user_profiles_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_profiles_config.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_custom_profile</GroupName>
          <Files>
            <File>
              <FileName>user_custs_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\custom_profile\user_custs_config.c</FilePath>
            </File>
            <File>
              <FileName>user_custs1_def.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\custom_profile\user_custs1_def.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_platform</GroupName>
          <Files>
            <File>
              <FileName>user_periph_setup.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\platform\user_periph_setup.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_app</GroupName>
          <Files>
            <File>
              <FileName>user_custs1_impl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\user_custs1_impl.c</FilePath>
            </File>
            <File>
              <FileName>user_peripheral.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\user_peripheral.c</FilePath>
            </File>
            <File>
              <FileName>scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\scheduler.c</FilePath>
            </File>
            <File>
              <FileName>ring_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\custom_profile\ring_buf.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>comm_manager</GroupName>
          <Files>
            <File>
              <FileName>comm_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\comm_task.c</FilePath>
            </File>
            <File>
              <FileName>SerialInterface.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\SerialInterface.c</FilePath>
            </File>
            <File>
              <FileName>comm_task.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\comm_task.h</FilePath>
            </File>
            <File>
              <FileName>Comm_Debug.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\Comm_Debug.h</FilePath>
            </File>
            <File>
              <FileName>UpperLayerInterface.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\UpperLayerInterface.h</FilePath>
            </File>
            <File>
              <FileName>SerialInterface.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\SerialInterface.h</FilePath>
            </File>
            <File>
              <FileName>UpperLayerInterface.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\UpperLayerInterface.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>comm_manager_lib</GroupName>
          <Files>
            <File>
              <FileName>Comm_Manager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\Comm_Manager.c</FilePath>
            </File>
            <File>
              <FileName>Comm_Manager.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\Comm_Manager.h</FilePath>
            </File>
            <File>
              <FileName>PrimitiveManager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\PrimitiveManager.c</FilePath>
            </File>
            <File>
              <FileName>PrimitiveQueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\PrimitiveQueue.c</FilePath>
            </File>
            <File>
              <FileName>PrimitiveQueue.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\PrimitiveQueue.h</FilePath>
            </File>
            <File>
              <FileName>TransportManager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\TransportManager.c</FilePath>
            </File>
            <File>
              <FileName>TransportQueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\TransportQueue.c</FilePath>
            </File>
            <File>
              <FileName>TransportQueue.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\TransportQueue.h</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
    <Target>
      <TargetName>DA14531</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060061::V5.06 update 1 (build 61)::ARMCC</pCCUsed>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM0P</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.4.5.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IROM(0x00000000,0x00040000) CPUTYPE("Cortex-M0+") CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM0P$Device\ARM\ARMCM0plus\Include\ARMCM0plus.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:ARMCM0P$Device\ARM\SVD\ARMCM0P.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\out_DA14531\Objects\</OutputDirectory>
          <OutputName>quent_531</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\out_DA14531\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf --bincombined --output=".\out_DA14531\Objects\@L.bin"  "!L"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>1</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0+</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0+</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>1</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>6</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile>..\..\..\sdk\common_project_files\misc\jlink_DA14531.ini</InitializationFile>
            <Driver>Segger\JL2CM3.dll</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>1</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>0</AdsALst>
            <AdsACrf>0</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M0+"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <useXO>0</useXO>
            <v6Lang>0</v6Lang>
            <v6LangP>0</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <VariousControls>
              <MiscControls>--thumb -c --preinclude da1458x_config_basic.h --preinclude da1458x_config_advanced.h --preinclude user_config.h --bss_threshold=0 --feedback=".\unused_531.txt"</MiscControls>
              <Define>__DA14531__ QUENT</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\sdk\app_modules\api;..\..\..\sdk\ble_stack\controller\em;..\..\..\sdk\ble_stack\controller\llc;..\..\..\sdk\ble_stack\controller\lld;..\..\..\sdk\ble_stack\controller\llm;..\..\..\sdk\ble_stack\ea\api;..\..\..\sdk\ble_stack\em\api;..\..\..\sdk\ble_stack\hci\api;..\..\..\sdk\ble_stack\hci\src;..\..\..\sdk\ble_stack\host\att;..\..\..\sdk\ble_stack\host\att\attc;..\..\..\sdk\ble_stack\host\att\attm;..\..\..\sdk\ble_stack\host\att\atts;..\..\..\sdk\ble_stack\host\gap;..\..\..\sdk\ble_stack\host\gap\gapc;..\..\..\sdk\ble_stack\host\gap\gapm;..\..\..\sdk\ble_stack\host\gatt;..\..\..\sdk\ble_stack\host\gatt\gattc;..\..\..\sdk\ble_stack\host\gatt\gattm;..\..\..\sdk\ble_stack\host\l2c\l2cc;..\..\..\sdk\ble_stack\host\l2c\l2cm;..\..\..\sdk\ble_stack\host\smp;..\..\..\sdk\ble_stack\host\smp\smpc;..\..\..\sdk\ble_stack\host\smp\smpm;..\..\..\sdk\ble_stack\profiles;..\..\..\sdk\ble_stack\profiles\anc;..\..\..\sdk\ble_stack\profiles\anc\ancc\api;..\..\..\sdk\ble_stack\profiles\anp;..\..\..\sdk\ble_stack\profiles\anp\anpc\api;..\..\..\sdk\ble_stack\profiles\anp\anps\api;..\..\..\sdk\ble_stack\profiles\bas\basc\api;..\..\..\sdk\ble_stack\profiles\bas\bass\api;..\..\..\sdk\ble_stack\profiles\bcs;..\..\..\sdk\ble_stack\profiles\bcs\bcsc\api;..\..\..\sdk\ble_stack\profiles\bcs\bcss\api;..\..\..\sdk\ble_stack\profiles\blp;..\..\..\sdk\ble_stack\profiles\blp\blpc\api;..\..\..\sdk\ble_stack\profiles\blp\blps\api;..\..\..\sdk\ble_stack\profiles\bms;..\..\..\sdk\ble_stack\profiles\bms\bmsc\api;..\..\..\sdk\ble_stack\profiles\bms\bmss\api;..\..\..\sdk\ble_stack\profiles\cpp;..\..\..\sdk\ble_stack\profiles\cpp\cppc\api;..\..\..\sdk\ble_stack\profiles\cpp\cpps\api;..\..\..\sdk\ble_stack\profiles\cscp;..\..\..\sdk\ble_stack\profiles\cscp\cscpc\api;..\..\..\sdk\ble_stack\profiles\cscp\cscps\api;..\..\..\sdk\ble_stack\profiles\cts;..\..\..\sdk\ble_stack\profiles\cts\ctsc\api;..\..\..\sdk\ble_stack\profiles\cts\ctss\api;..\..\..\sdk\ble_stack\profiles\custom;..\..\..\sdk\ble_stack\profiles\custom\custs\api;..\..\..\sdk\ble_stack\profiles\dis\disc\api;..\..\..\sdk\ble_stack\profiles\dis\diss\api;..\..\..\sdk\ble_stack\profiles\find;..\..\..\sdk\ble_stack\profiles\find\findl\api;..\..\..\sdk\ble_stack\profiles\find\findt\api;..\..\..\sdk\ble_stack\profiles\gatt\gatt_client\api;..\..\..\sdk\ble_stack\profiles\glp;..\..\..\sdk\ble_stack\profiles\glp\glpc\api;..\..\..\sdk\ble_stack\profiles\glp\glps\api;..\..\..\sdk\ble_stack\profiles\hogp;..\..\..\sdk\ble_stack\profiles\hogp\hogpbh\api;..\..\..\sdk\ble_stack\profiles\hogp\hogpd\api;..\..\..\sdk\ble_stack\profiles\hogp\hogprh\api;..\..\..\sdk\ble_stack\profiles\hrp;..\..\..\sdk\ble_stack\profiles\hrp\hrpc\api;..\..\..\sdk\ble_stack\profiles\hrp\hrps\api;..\..\..\sdk\ble_stack\profiles\htp;..\..\..\sdk\ble_stack\profiles\htp\htpc\api;..\..\..\sdk\ble_stack\profiles\htp\htpt\api;..\..\..\sdk\ble_stack\profiles\lan;..\..\..\sdk\ble_stack\profiles\lan\lanc\api;..\..\..\sdk\ble_stack\profiles\lan\lans\api;..\..\..\sdk\ble_stack\profiles\pasp;..\..\..\sdk\ble_stack\profiles\pasp\paspc\api;..\..\..\sdk\ble_stack\profiles\pasp\pasps\api;..\..\..\sdk\ble_stack\profiles\prox\proxm\api;..\..\..\sdk\ble_stack\profiles\prox\proxr\api;..\..\..\sdk\ble_stack\profiles\rscp;..\..\..\sdk\ble_stack\profiles\rscp\rscpc\api;..\..\..\sdk\ble_stack\profiles\rscp\rscps\api;..\..\..\sdk\ble_stack\profiles\scpp;..\..\..\sdk\ble_stack\profiles\scpp\scppc\api;..\..\..\sdk\ble_stack\profiles\scpp\scpps\api;..\..\..\sdk\ble_stack\profiles\suota\suotar\api;..\..\..\sdk\ble_stack\profiles\tip;..\..\..\sdk\ble_stack\profiles\tip\tipc\api;..\..\..\sdk\ble_stack\profiles\tip\tips\api;..\..\..\sdk\ble_stack\profiles\uds;..\..\..\sdk\ble_stack\profiles\uds\udsc\api;..\..\..\sdk\ble_stack\profiles\uds\udss\api;..\..\..\sdk\ble_stack\profiles\wss;..\..\..\sdk\ble_stack\profiles\wss\wssc\api;..\..\..\sdk\ble_stack\profiles\wss\wsss\api;..\..\..\sdk\ble_stack\rwble;..\..\..\sdk\ble_stack\rwble_hl;..\..\..\sdk\common_project_files;..\..\..\sdk\platform\arch;..\..\..\sdk\platform\arch\boot;..\..\..\sdk\platform\arch\boot\ARM;..\..\..\sdk\platform\arch\boot\GCC;..\..\..\sdk\platform\arch\compiler;..\..\..\sdk\platform\arch\compiler\ARM;..\..\..\sdk\platform\arch\compiler\GCC;..\..\..\sdk\platform\arch\ll;..\..\..\sdk\platform\arch\main;..\..\..\sdk\platform\core_modules\arch_console;..\..\..\sdk\platform\core_modules\common\api;..\..\..\sdk\platform\core_modules\crypto;..\..\..\sdk\platform\core_modules\dbg\api;..\..\..\sdk\platform\core_modules\gtl\api;..\..\..\sdk\platform\core_modules\gtl\src;..\..\..\sdk\platform\core_modules\h4tl\api;..\..\..\sdk\platform\core_modules\ke\api;..\..\..\sdk\platform\core_modules\ke\src;..\..\..\sdk\platform\core_modules\nvds\api;..\..\..\sdk\platform\core_modules\rf\api;..\..\..\sdk\platform\core_modules\rwip\api;..\..\..\sdk\platform\driver\adc;..\..\..\sdk\platform\driver\battery;..\..\..\sdk\platform\driver\ble;..\..\..\sdk\platform\driver\dma;..\..\..\sdk\platform\driver\gpio;..\..\..\sdk\platform\driver\hw_otpc;..\..\..\sdk\platform\driver\i2c;..\..\..\sdk\platform\driver\i2c_eeprom;..\..\..\sdk\platform\driver\pdm;..\..\..\sdk\platform\driver\reg;..\..\..\sdk\platform\driver\rtc;..\..\..\sdk\platform\driver\spi;..\..\..\sdk\platform\driver\spi_flash;..\..\..\sdk\platform\driver\spi_hci;..\..\..\sdk\platform\driver\syscntl;..\..\..\sdk\platform\driver\systick;..\..\..\sdk\platform\driver\timer;..\..\..\sdk\platform\driver\trng;..\..\..\sdk\platform\driver\uart;..\..\..\sdk\platform\driver\wkupct_quadec;..\..\..\sdk\platform\include;..\..\..\sdk\platform\system_library\include;..\..\..\third_party\hash;..\..\..\third_party\irng;..\..\..\third_party\rand;.\..\src;.\..\src\config;.\..\src\custom_profile;..\..\..\sdk\platform\utilities\otp_cs;..\..\..\sdk\platform\utilities\otp_hdr;..\..\..\sdk\platform\include\CMSIS\5.6.0\Include;..\..\..\sdk\platform\driver\wifi;..\src\COMM_MANAGER;..\src\COMM_MANAGER\Lib</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>1</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x00000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>..\..\..\sdk\common_project_files\scatterfiles\DA14531.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc>--feedback=".\unused_531.txt" ..\..\..\sdk\common_project_files\misc\da14531_symbols.txt --symdefs=quent_531_symdef.txt --any_placement=best_fit --datacompressor off</Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>sdk_boot</GroupName>
          <Files>
            <File>
              <FileName>system_DA14585_586.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\boot\system_DA14585_586.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>0</vShortEn>
                    <vShortWch>0</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>startup_DA14585_586.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\sdk\platform\arch\boot\ARM\startup_DA14585_586.s</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Aads>
                    <interw>2</interw>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <thumb>2</thumb>
                    <SplitLS>2</SplitLS>
                    <SwStkChk>2</SwStkChk>
                    <NoWarn>2</NoWarn>
                    <uSurpInc>2</uSurpInc>
                    <useXO>2</useXO>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Aads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>system_DA14531.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\boot\system_DA14531.c</FilePath>
            </File>
            <File>
              <FileName>startup_DA14531.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\sdk\platform\arch\boot\ARM\startup_DA14531.s</FilePath>
            </File>
            <File>
              <FileName>hardfault_handler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\hardfault_handler.c</FilePath>
            </File>
            <File>
              <FileName>nmi_handler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\nmi_handler.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_arch</GroupName>
          <Files>
            <File>
              <FileName>arch_console.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\arch_console\arch_console.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>2</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>1</vShortEn>
                    <vShortWch>1</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>nvds.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\nvds\src\nvds.c</FilePath>
            </File>
            <File>
              <FileName>arch_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_main.c</FilePath>
            </File>
            <File>
              <FileName>jump_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\jump_table.c</FilePath>
            </File>
            <File>
              <FileName>arch_sleep.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_sleep.c</FilePath>
            </File>
            <File>
              <FileName>arch_system.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_system.c</FilePath>
            </File>
            <File>
              <FileName>arch_hibernation.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_hibernation.c</FilePath>
            </File>
            <File>
              <FileName>arch_rom.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\arch\main\arch_rom.c</FilePath>
            </File>
            <File>
              <FileName>chacha20.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\third_party\rand\chacha20.c</FilePath>
            </File>
            <File>
              <FileName>hash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\third_party\hash\hash.c</FilePath>
            </File>
            <File>
              <FileName>da14585_586.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\sdk\platform\system_library\output\Keil_5\da14585_586.lib</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds/>
              </FileOption>
            </File>
            <File>
              <FileName>da14531.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\..\sdk\platform\system_library\output\Keil_5\da14531.lib</FilePath>
            </File>
            <File>
              <FileName>otp_cs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\utilities\otp_cs\otp_cs.c</FilePath>
            </File>
            <File>
              <FileName>otp_hdr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\utilities\otp_hdr\otp_hdr.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_driver</GroupName>
          <Files>
            <File>
              <FileName>syscntl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\syscntl\syscntl.c</FilePath>
            </File>
            <File>
              <FileName>gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\gpio\gpio.c</FilePath>
            </File>
            <File>
              <FileName>hw_otpc_58x.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\hw_otpc\hw_otpc_58x.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>0</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>0</vShortEn>
                    <vShortWch>0</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>hw_otpc_531.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\hw_otpc\hw_otpc_531.c</FilePath>
            </File>
            <File>
              <FileName>uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\uart\uart.c</FilePath>
            </File>
            <File>
              <FileName>trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\trng\trng.c</FilePath>
            </File>
            <File>
              <FileName>dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\dma\dma.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>2</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>1</vShortEn>
                    <vShortWch>1</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>wlan_coex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\wifi\wlan_coex.c</FilePath>
            </File>
            <File>
              <FileName>adc_531.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\driver\adc\adc_531.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_ble</GroupName>
          <Files>
            <File>
              <FileName>rwble.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\rwble\rwble.c</FilePath>
            </File>
            <File>
              <FileName>rwip.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\rwip\src\rwip.c</FilePath>
            </File>
            <File>
              <FileName>rf_585.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\rf\src\rf_585.c</FilePath>
            </File>
            <File>
              <FileName>ble_arp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\rf\src\ble_arp.c</FilePath>
            </File>
            <File>
              <FileName>rf_531.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\platform\core_modules\rf\src\rf_531.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_profiles</GroupName>
          <Files>
            <File>
              <FileName>attm_db_128.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\host\att\attm\attm_db_128.c</FilePath>
            </File>
            <File>
              <FileName>prf_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\prf_utils.c</FilePath>
            </File>
            <File>
              <FileName>custom_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\custom\custom_common.c</FilePath>
            </File>
            <File>
              <FileName>diss.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\dis\diss\src\diss.c</FilePath>
            </File>
            <File>
              <FileName>diss_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\dis\diss\src\diss_task.c</FilePath>
            </File>
            <File>
              <FileName>custs1.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\custom\custs\src\custs1.c</FilePath>
            </File>
            <File>
              <FileName>custs1_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\custom\custs\src\custs1_task.c</FilePath>
            </File>
            <File>
              <FileName>prf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\ble_stack\profiles\prf.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sdk_app</GroupName>
          <Files>
            <File>
              <FileName>app_default_handlers.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_default_hnd\app_default_handlers.c</FilePath>
            </File>
            <File>
              <FileName>app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_common\app.c</FilePath>
            </File>
            <File>
              <FileName>app_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_common\app_task.c</FilePath>
            </File>
            <File>
              <FileName>app_security.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_sec\app_security.c</FilePath>
            </File>
            <File>
              <FileName>app_security_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_sec\app_security_task.c</FilePath>
            </File>
            <File>
              <FileName>app_bass.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_bass\app_bass.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>2</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>1</vShortEn>
                    <vShortWch>1</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_bass_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_bass\app_bass_task.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>2</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>1</vShortEn>
                    <vShortWch>1</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_findme.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_findme\app_findme.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>2</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>1</vShortEn>
                    <vShortWch>1</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_findme_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_findme\app_findme_task.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>2</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>1</vShortEn>
                    <vShortWch>1</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_proxr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_proxr\app_proxr.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>2</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>1</vShortEn>
                    <vShortWch>1</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_proxr_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_proxr\app_proxr_task.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>2</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>1</vShortEn>
                    <vShortWch>1</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_diss.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_diss\app_diss.c</FilePath>
            </File>
            <File>
              <FileName>app_diss_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_diss\app_diss_task.c</FilePath>
            </File>
            <File>
              <FileName>app_suotar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_suotar\app_suotar.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>2</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>1</vShortEn>
                    <vShortWch>1</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_suotar_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_suotar\app_suotar_task.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>0</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>2</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>1</vShortEn>
                    <vShortWch>1</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>app_entry_point.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_entry\app_entry_point.c</FilePath>
            </File>
            <File>
              <FileName>app_msg_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_common\app_msg_utils.c</FilePath>
            </File>
            <File>
              <FileName>app_easy_msg_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_easy\app_easy_msg_utils.c</FilePath>
            </File>
            <File>
              <FileName>app_easy_security.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_easy\app_easy_security.c</FilePath>
            </File>
            <File>
              <FileName>app_easy_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_easy\app_easy_timer.c</FilePath>
            </File>
            <File>
              <FileName>app_customs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_custs\app_customs.c</FilePath>
            </File>
            <File>
              <FileName>app_customs_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_custs\app_customs_task.c</FilePath>
            </File>
            <File>
              <FileName>app_customs_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_custs\app_customs_common.c</FilePath>
            </File>
            <File>
              <FileName>app_bond_db.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_bond_db\app_bond_db.c</FilePath>
            </File>
            <File>
              <FileName>app_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_common\app_utils.c</FilePath>
            </File>
            <File>
              <FileName>app_easy_whitelist.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\sdk\app_modules\src\app_easy\app_easy_whitelist.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_config</GroupName>
          <Files>
            <File>
              <FileName>da1458x_config_advanced.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\da1458x_config_advanced.h</FilePath>
            </File>
            <File>
              <FileName>da1458x_config_basic.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\da1458x_config_basic.h</FilePath>
            </File>
            <File>
              <FileName>user_callback_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_callback_config.h</FilePath>
            </File>
            <File>
              <FileName>user_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_config.h</FilePath>
            </File>
            <File>
              <FileName>user_modules_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_modules_config.h</FilePath>
            </File>
            <File>
              <FileName>user_periph_setup.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_periph_setup.h</FilePath>
            </File>
            <File>
              <FileName>user_profiles_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\config\user_profiles_config.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_custom_profile</GroupName>
          <Files>
            <File>
              <FileName>user_custs_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\custom_profile\user_custs_config.c</FilePath>
            </File>
            <File>
              <FileName>user_custs1_def.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\custom_profile\user_custs1_def.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_platform</GroupName>
          <Files>
            <File>
              <FileName>user_periph_setup.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\platform\user_periph_setup.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user_app</GroupName>
          <Files>
            <File>
              <FileName>user_custs1_impl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\user_custs1_impl.c</FilePath>
            </File>
            <File>
              <FileName>user_peripheral.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\user_peripheral.c</FilePath>
            </File>
            <File>
              <FileName>scheduler.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\scheduler.c</FilePath>
              <FileOption>
                <CommonProperty>
                  <UseCPPCompiler>2</UseCPPCompiler>
                  <RVCTCodeConst>0</RVCTCodeConst>
                  <RVCTZI>0</RVCTZI>
                  <RVCTOtherData>0</RVCTOtherData>
                  <ModuleSelection>0</ModuleSelection>
                  <IncludeInBuild>1</IncludeInBuild>
                  <AlwaysBuild>2</AlwaysBuild>
                  <GenerateAssemblyFile>2</GenerateAssemblyFile>
                  <AssembleAssemblyFile>2</AssembleAssemblyFile>
                  <PublicsOnly>2</PublicsOnly>
                  <StopOnExitCode>11</StopOnExitCode>
                  <CustomArgument></CustomArgument>
                  <IncludeLibraryModules></IncludeLibraryModules>
                  <ComprImg>1</ComprImg>
                </CommonProperty>
                <FileArmAds>
                  <Cads>
                    <interw>2</interw>
                    <Optim>0</Optim>
                    <oTime>2</oTime>
                    <SplitLS>2</SplitLS>
                    <OneElfS>2</OneElfS>
                    <Strict>2</Strict>
                    <EnumInt>2</EnumInt>
                    <PlainCh>2</PlainCh>
                    <Ropi>2</Ropi>
                    <Rwpi>2</Rwpi>
                    <wLevel>2</wLevel>
                    <uThumb>2</uThumb>
                    <uSurpInc>2</uSurpInc>
                    <uC99>2</uC99>
                    <useXO>2</useXO>
                    <v6Lang>0</v6Lang>
                    <v6LangP>0</v6LangP>
                    <vShortEn>1</vShortEn>
                    <vShortWch>1</vShortWch>
                    <VariousControls>
                      <MiscControls></MiscControls>
                      <Define></Define>
                      <Undefine></Undefine>
                      <IncludePath></IncludePath>
                    </VariousControls>
                  </Cads>
                </FileArmAds>
              </FileOption>
            </File>
            <File>
              <FileName>ring_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\custom_profile\ring_buf.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>comm_manager</GroupName>
          <Files>
            <File>
              <FileName>comm_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\comm_task.c</FilePath>
            </File>
            <File>
              <FileName>SerialInterface.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\SerialInterface.c</FilePath>
            </File>
            <File>
              <FileName>comm_task.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\comm_task.h</FilePath>
            </File>
            <File>
              <FileName>Comm_Debug.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\Comm_Debug.h</FilePath>
            </File>
            <File>
              <FileName>UpperLayerInterface.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\UpperLayerInterface.h</FilePath>
            </File>
            <File>
              <FileName>SerialInterface.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\SerialInterface.h</FilePath>
            </File>
            <File>
              <FileName>UpperLayerInterface.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\UpperLayerInterface.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>comm_manager_lib</GroupName>
          <Files>
            <File>
              <FileName>Comm_Manager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\Comm_Manager.c</FilePath>
            </File>
            <File>
              <FileName>Comm_Manager.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\Comm_Manager.h</FilePath>
            </File>
            <File>
              <FileName>PrimitiveManager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\PrimitiveManager.c</FilePath>
            </File>
            <File>
              <FileName>PrimitiveQueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\PrimitiveQueue.c</FilePath>
            </File>
            <File>
              <FileName>PrimitiveQueue.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\PrimitiveQueue.h</FilePath>
            </File>
            <File>
              <FileName>TransportManager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\TransportManager.c</FilePath>
            </File>
            <File>
              <FileName>TransportQueue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\TransportQueue.c</FilePath>
            </File>
            <File>
              <FileName>TransportQueue.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\src\COMM_MANAGER\Lib\TransportQueue.h</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

</Project>
