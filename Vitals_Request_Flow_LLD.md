# Low-Level Design Document: Vitals Request Flow

## 1. Introduction

This document describes the low-level design and code flow when requesting any vital sign (such as heart rate, blood oxygen, ECG, body temperature, or blood pressure) from the system. The document outlines the components involved, their interactions, and the sequence of operations from the initial request to the final delivery of vital data.

## 2. System Architecture Overview

The system consists of the following main components:

1. **BLE Module (DA14531 SoC)**:
   - Handles BLE communication with external devices (like smartphones)
   - Implements the GATT server with services and characteristics for vitals data
   - Processes BLE events and forwards requests to the Communication Manager
   - Runs on Dialog Semiconductor DA14531 SoC with Bluetooth Low Energy capabilities

2. **STM32 Co-processor**:
   - Manages sensors and vital sign measurements
   - Contains the firmware for controlling various sensors (heart rate, SpO2, temperature, etc.)
   - Processes measurement requests and returns data or error codes
   - Handles the actual sensor interfacing and data acquisition

3. **GATT Server**:
   - Implemented on the BLE Module
   - Provides standardized BLE services and characteristics for vitals data
   - Handles notifications and indications to client devices
   - Manages client connections and data transfer

4. **Communication Manager**:
   - Acts as a bridge between the BLE Module and STM32
   - Manages the command and response protocol between components
   - Handles queuing, retries, and error management
   - Consists of several sub-components:
     - Primitive Manager: Handles command primitives
     - Transport Manager: Manages the physical transport layer
     - Upper Layer Interface: Provides API for the application layer

## 3. Vitals Request Flow

### 3.1 Connection and Service Discovery

Before any vitals can be requested, the client device must connect to the BLE device and discover its services:

1. **Connection Establishment**:
   - The client device scans for BLE devices advertising the appropriate service UUID
   - When the target device is found, the client initiates a connection
   - The BLE device accepts the connection and establishes a link

2. **Service Discovery**:
   - The client performs service discovery to find the custom service for vitals
   - It discovers the characteristics within that service
   - It identifies the characteristic for requesting vitals (VITAL_VAL)

3. **Enabling Indications**:
   - For each characteristic that will provide data via indications, the client must enable indications
   - This is done by writing to the Client Characteristic Configuration Descriptor (CCCD)
   - The client writes the value 0x0002 to enable indications
   - The BLE device acknowledges this and will now send indications when data is available

```c
// Client-side code (Android example) for enabling indications
private void enableVitalIndications() {
    BluetoothGattCharacteristic characteristic = findVitalCharacteristic();
    if (characteristic == null) {
        Log.e(TAG, "Vital characteristic not found");
        return;
    }

    // Enable indications
    BluetoothGattDescriptor descriptor = characteristic.getDescriptor(
            UUID.fromString("00002902-0000-1000-8000-00805f9b34fb")); // CCCD UUID
    descriptor.setValue(BluetoothGattDescriptor.ENABLE_INDICATION_VALUE);
    mBluetoothGatt.writeDescriptor(descriptor);
    Log.d(TAG, "Enabled indications for vital characteristic");
}
```

```c
// Server-side handling of CCCD writes
static void handle_cccd_write(uint8_t conidx, uint16_t handle, uint16_t value) {
    // Check which characteristic this CCCD belongs to
    uint16_t char_handle = handle - 1; // CCCDs are typically right after the characteristic

    switch (char_handle) {
        case VITAL_VAL:
            // Store the CCCD value for this connection
            app_env[conidx].vital_ind_cfg = value;
            // Check if indications are being enabled (0x0002) or disabled (0x0000)
            if (value == 0x0002) {
                // Indications enabled
                CALLBACK_ARGS_2(user_app_callbacks.on_vital_ind_enabled, conidx, VITAL_VAL);
                // Log the event
                PRINT_INFO("Vital indications enabled for connection %d\r\n", conidx);
            } else if (value == 0x0000) {
                // Indications disabled
                CALLBACK_ARGS_2(user_app_callbacks.on_vital_ind_disabled, conidx, VITAL_VAL);
                // Log the event
                PRINT_INFO("Vital indications disabled for connection %d\r\n", conidx);
            }
            break;

        case SVC1_ECG_1_VAL:
            // Store the CCCD value for ECG characteristic
            app_env[conidx].ecg_ind_cfg = value;
            if (value == 0x0002) {
                // Indications enabled
                CALLBACK_ARGS_2(user_app_callbacks.on_ecg_ind_enabled, conidx, SVC1_ECG_1_VAL);
                PRINT_INFO("ECG indications enabled for connection %d\r\n", conidx);
            } else if (value == 0x0000) {
                // Indications disabled
                CALLBACK_ARGS_2(user_app_callbacks.on_ecg_ind_disabled, conidx, SVC1_ECG_1_VAL);
                PRINT_INFO("ECG indications disabled for connection %d\r\n", conidx);
            }
            break;

        case USR_NAME_1_VAL:
            // Store the CCCD value for user info characteristic
            app_env[conidx].usr_info_ntf_cfg = value;
            if (value == 0x0001) { // Notifications use 0x0001
                // Notifications enabled
                CALLBACK_ARGS_2(user_app_callbacks.on_usr_info_ntf_enabled, conidx, USR_NAME_1_VAL);
                PRINT_INFO("User info notifications enabled for connection %d\r\n", conidx);
            } else if (value == 0x0000) {
                // Notifications disabled
                CALLBACK_ARGS_2(user_app_callbacks.on_usr_info_ntf_disabled, conidx, USR_NAME_1_VAL);
                PRINT_INFO("User info notifications disabled for connection %d\r\n", conidx);
            }
            break;

        default:
            // Unknown characteristic
            PRINT_WARN("CCCD write for unknown characteristic handle: %d\r\n", char_handle);
            break;
    }

    // Check if we need to update the connection parameters based on enabled indications
    if (app_env[conidx].vital_ind_cfg == 0x0002 || app_env[conidx].ecg_ind_cfg == 0x0002) {
        // If vitals or ECG indications are enabled, we might want to update connection parameters
        // for better throughput or lower latency
        if (!app_env[conidx].params_updated) {
            // Update connection parameters for better performance
            struct gapc_conn_param conn_param;
            conn_param.intv_min = 8;  // 10ms (8 * 1.25ms)
            conn_param.intv_max = 16; // 20ms (16 * 1.25ms)
            conn_param.latency = 0;
            conn_param.time_out = 200; // 2s (200 * 10ms)

            // Request the parameter update
            app_easy_gap_param_update_start(conidx, &conn_param);
            app_env[conidx].params_updated = true;

            PRINT_INFO("Connection parameters update requested for connection %d\r\n", conidx);
        }
    }
}
```

### 3.2 High-Level Flow

Once the connection is established and indications are enabled, the vitals request flow is as follows:

```
[Client Device] → [BLE Module (GATT Server)] → [Communication Manager] → [STM32] →
[Sensors] → [STM32] → [Communication Manager] → [BLE Module] → [Client Device]
```

### 3.3 Detailed Flow Diagram

```
Client Device                BLE Module                  Communication Manager                STM32                     Sensors
    |                            |                               |                              |                          |
    | Request Vital              |                               |                              |                          |
    |--------------------------->|                               |                              |                          |
    |                            | UpdateVitalsReq()             |                              |                          |
    |                            |------------------------------>|                              |                          |
    |                            |                               | notify_stm(COMMAND, vital)   |                          |
    |                            |                               |----------------------------->|                          |
    |                            |                               |                              | Initiate Measurement     |
    |                            |                               |                              |------------------------->|
    |                            |                               |                              |                          |
    |                            |                               |                              | Measurement Data         |
    |                            |                               |                              |<-------------------------|
    |                            |                               | Response/Indication          |                          |
    |                            |                               |<-----------------------------|                          |
    |                            | sendOverBle()                 |                              |                          |
    |                            |<------------------------------|                              |                          |
    |                            | Send_To_Gatt_Client()         |                              |                          |
    |                            |------------------------------>|                              |                          |
    | Vital Data                 |                               |                              |                          |
    |<---------------------------|                               |                              |                          |
```

## 4. Detailed Code Flow

### 4.1 Initial Request Processing

1. **Client writes to GATT characteristic**:
   - The client device (e.g., smartphone app) connects to the BLE device via Bluetooth
   - The client identifies the GATT characteristic for vitals requests (VITAL_VAL)
   - It performs a GATT write operation with the vital ID (e.g., 0x01 for heart rate)
   - This write operation triggers a callback in the BLE stack on the device

   ```c
   // Client-side code (Android example)
   private void requestHeartRateVital() {
       if (mGattCharacteristic == null) {
           Log.e(TAG, "GATT characteristic not found");
           return;
       }

       // Create the request data - just the vital ID
       byte[] data = new byte[1];
       data[0] = HEART_RATE_ID; // 0x01

       // Write to the characteristic
       mGattCharacteristic.setValue(data);
       mBluetoothGatt.writeCharacteristic(mGattCharacteristic);
       Log.d(TAG, "Heart rate vital request sent");
   }
   ```

2. **GATT write handler processes request**:
   - The BLE stack receives the write request and routes it to the appropriate handler
   - The characteristic's write handler is invoked with the written data
   - The handler identifies which vital was requested based on the data value
   - It then calls `UpdateVitalsReq()` with a pointer to the vital ID

   ```c
   // From custom_profile/user_custs1_def.c
   static att_perm_type VITAL_VAL_perm = PERM(WR, ENABLE) | PERM(WRITE_REQ, ENABLE) | PERM(IND, ENABLE);

   // GATT write handler
   static void user_svc1_write_val_handler(uint8_t conidx, uint16_t handle, uint16_t length, uint8_t *value)
   {
       // Handle based on attribute handle
       switch (handle) {
           case VITAL_VAL:
               // Process vital request
               UpdateVitalsReq(value);
               break;

           // Other characteristics...

           default:
               break;
       }
   }

   // Register the handler in initialization
   void user_custs1_init(void)
   {
       // Register write handler for the service
       custs1_init(user_svc1_write_val_handler);
   }
   ```

3. **UpdateVitalsReq() function**:
   ```c
   void UpdateVitalsReq(uint8_t *Vital_Data)
   {
       uint8_t Return = 0x00;

       switch(*Vital_Data)
       {
           case HEART_RATE:
               Return = notify_stm(COMMAND, comm_wHR, NULL, NULL);
               break;

           case BLOOD_OXYGEN:
               Return = notify_stm(COMMAND, comm_wSPO2, NULL, NULL);
               break;
           case BODY_TEMPERATURE:
               Return = notify_stm(COMMAND, comm_wCBT, NULL, NULL);
               break;
           case BLOOD_PRESSURE:
               Return = notify_stm(COMMAND, comm_wBP, NULL, NULL);
               break;
           // Other vitals...
       }
   }
   ```

   - This function is the main entry point for processing vital requests
   - It takes a pointer to the vital ID as input
   - It uses a switch statement to determine which vital was requested
   - For each vital type, it calls `notify_stm()` with the appropriate command ID:
     - Heart Rate: `comm_wHR`
     - Blood Oxygen: `comm_wSPO2`
     - Body Temperature: `comm_wCBT`
     - Blood Pressure: `comm_wBP`
   - The function passes NULL for data and length since this is a request without payload
   - The return value indicates whether the request was successfully queued

4. **notify_stm() function**:
   ```c
   uint8_t notify_stm(uint8_t id, uint16_t Sub_Id, uint8_t *Data, uint16_t Len)
   {
       static uint8_t payloadPlaceHolder[160] __SECTION_ZERO("free_area");
       memset(payloadPlaceHolder, 0, sizeof(payloadPlaceHolder));

       if(Data!=NULL)
       {
           memcpy(payloadPlaceHolder, Data, Len);
       }

       if(id == INDICATION)
       {
           return Com_Send_Data(Sub_Id, payloadPlaceHolder, Len);
       }
       else if(id == COMMAND)
       {
           return Com_Get_Data(Sub_Id);
       }

       return -1;
   }
   ```

   - This function is the bridge between the BLE module and the Communication Manager
   - It allocates a buffer (`payloadPlaceHolder`) for the command data
   - For commands with data, it copies the data into the buffer
   - It determines the type of operation based on the `id` parameter:
     - `INDICATION`: For sending data to STM32 (calls `Com_Send_Data()`)
     - `COMMAND`: For requesting data from STM32 (calls `Com_Get_Data()`)
   - For vital requests, it uses `COMMAND` mode since we're requesting a measurement

5. **Com_Get_Data() function**:
   ```c
   int32_t Com_Get_Data(enum comm_subid subid)
   {
       Comm_Queue_t msg;
       msg.event.pt.trigger = cmd_tx;
       msg.module = ul;
       msg.event.pt.sub_id = subid;
       msg.event.pt.retry = 3;
       triggerCommMngr(&msg);
       return 1;
   }
   ```

   - This function prepares a command message for the Communication Manager
   - It creates a queue entry (`Comm_Queue_t`) with:
     - `trigger`: Set to `cmd_tx` (command transmission)
     - `module`: Set to `ul` (upper layer)
     - `sub_id`: The vital command ID (e.g., `comm_wHR`)
     - `retry`: Number of retries (typically 3)
   - It calls `triggerCommMngr()` to queue the command for processing
   - This function returns immediately, as the command processing happens asynchronously

### 4.2 Communication Manager Processing

1. **Communication Manager queues the request**:
   - The `triggerCommMngr()` function adds the command to a FIFO queue
   - The queue is implemented as a circular buffer to efficiently manage commands
   - Each queue entry contains all the information needed to process the command:
     - `trigger`: Set to `cmd_tx` (command transmission)
     - `module`: Set to `ul` (upper layer)
     - `sub_id`: The vital command ID (e.g., `comm_wHR`)
     - `retry`: Number of retries (typically 3)
   - The Communication Manager has a task that periodically checks the queue
   - When a new command is detected, it starts the processing sequence

   ```c
   // From COMM_MANAGER/Lib/Comm_Manager.c
   int32_t Com_Get_Data(enum comm_subid subid)
   {
       Comm_Queue_t msg;
       msg.event.pt.trigger = cmd_tx;
       msg.module = ul;
       msg.event.pt.sub_id = subid;
       msg.event.pt.retry = 3;
       COM_PRINTI("In Com_Get_Data %d \n", subid);
       triggerCommMngr(&msg);
       return 1;
   }

   // Queue management in the Communication Manager
   static int32_t triggerCommMngr(Comm_Queue_t *msg)
   {
       if(commMngrQueue.count >= COMM_QUEUE_SIZE)
       {
           return 0;
       }

       commMngrQueue.queue[commMngrQueue.head] = *msg;
       commMngrQueue.head = (commMngrQueue.head + 1) % COMM_QUEUE_SIZE;
       commMngrQueue.count++;

       return 1;
   }
   ```

2. **Primitive Manager processes the request**:
   - The Primitive Manager is a state machine that handles command processing
   - It goes through several states for each command:
     - `p_crecv`: Initial state where the command is received
       - Copies the command data to a local buffer
       - Checks if the command is serviceable using `CmdIsServicable()`
       - If serviceable, sends an ACK to the Transport Manager
       - Transitions to the `p_cexec` state
     - `p_cexec`: Command execution state
       - Calls `BLE_ExecuteCommand()` with the command sub-ID
       - Based on the return value, it decides the next state:
         - `UL_S`: Stay in current state (waiting)
         - `UL_F`: Transition to `p_cr_msgfail` (command failed)
         - `UL_C`: Transition to `p_rsend` (send to STM32)
         - `UL_BUSY`: Transition to `p_rsend` with busy status
     - `p_rsend`: Response sending state
       - Prepares a response packet for the Transport Manager
       - Includes the command status (success, failure, busy)
       - Sends the packet to the Transport Manager
       - Transitions to the next state based on the result
   - The Transport Manager handles the physical communication with STM32 (likely UART)
   - The command includes a vital ID that tells STM32 which measurement to take

   ```c
   // From COMM_MANAGER/PrimitiveManager.c
   static int handle_p_crecv()
   {
       // Check if the command is serviceable
       #if ENABLE_CLOSE_STATE
       usageCount++;
       #endif
       CopyTrigger(&crTrigger, &temp);
       SetTransportTrigger(t_ack);
       return(STATE_WAIT);
   }

   static int handle_p_cexec()
   {
       switch(Execute_Command_API(crTrigger.sub_id, crTrigger.payload, crTrigger.len))
       {
       case UL_S:
           return(STATE_WAIT);
       case UL_F:
           return p_cr_changeState(p_cr_msgfail);
       case UL_C:
           return p_cr_changeState(p_rsend);
       case UL_BUSY:
           cr_exec_status = ERR_VITAL_BUSY;
           return p_cr_changeState(p_rsend);
       }
       return 0;
   }

   static int handle_p_rsend()
   {
       struct _Exchange resp;
       memset(&resp, 0, sizeof(resp));
       resp.SUB_ID = crTrigger.sub_id;
       resp.ID = RESPONSE;

       // Update requester information
       _platform_upd_requester(&resp, cr_exec_status);

       // Send to Transport Manager
       SetTransportTrigger(t_send);
       return p_cr_changeState(p_close);
   }
   ```

3. **STM32 processes the request**:
   - The Transport Manager sends the command to the STM32 via UART
   - The STM32 receives the command and identifies the requested vital
   - It performs several checks before initiating the measurement:
     - Is the sensor available?
     - Is there enough battery power?
     - Is another measurement already in progress?
   - If all checks pass, it activates the appropriate sensor:
     - Heart Rate: Activates the optical sensor (PPG)
     - Blood Oxygen: Activates dual-wavelength optical sensor
     - Body Temperature: Activates temperature sensor
     - Blood Pressure: Activates pressure sensor
     - ECG: Activates electrode sensors
   - The measurement process varies by vital type:
     - Heart Rate: Captures PPG signal, applies filtering, counts peaks
     - Blood Oxygen: Analyzes red and infrared light absorption ratios
     - Body Temperature: Reads sensor value, applies calibration
     - Blood Pressure: Measures pressure changes, applies algorithms
     - ECG: Captures electrical signals, applies filtering, identifies features
   - If the measurement is successful, it prepares a response packet with:
     - Vital ID
     - Measurement value
     - Timestamp
     - Origin information
   - If an error occurs, it sets the error flag and error code

   ```c
   // STM32 side code (conceptual representation)
   void process_vital_request(uint8_t vital_id)
   {
       // Check if sensor is available
       if (!is_sensor_available(vital_id)) {
           send_error_response(vital_id, ERR_VITAL_DECLINE);
           return;
       }

       // Check battery level
       if (get_battery_level() < MIN_BATTERY_FOR_MEASUREMENT) {
           send_error_response(vital_id, ERR_VITAL_LBATT);
           return;
       }

       // Check if already measuring
       if (is_measurement_in_progress()) {
           send_error_response(vital_id, ERR_VITAL_BUSY);
           return;
       }

       // Start measurement based on vital type
       switch (vital_id) {
           case HEART_RATE:
               start_heart_rate_measurement();
               break;
           case BLOOD_OXYGEN:
               start_spo2_measurement();
               break;
           case BODY_TEMPERATURE:
               start_temperature_measurement();
               break;
           case BLOOD_PRESSURE:
               start_blood_pressure_measurement();
               break;
           case EKG:
               start_ecg_measurement();
               break;
           default:
               send_error_response(vital_id, ERR_INVALID_VITAL);
               return;
       }

       // Wait for measurement completion
       // This would typically be handled by an interrupt or callback

       // When measurement completes, prepare response
       uint8_t response[8];
       response[0] = vital_id;

       if (measurement_successful()) {
           // Fill in measurement value
           if (vital_id == HEART_RATE || vital_id == BLOOD_OXYGEN) {
               response[1] = 0; // MSB
               response[2] = get_measurement_value(); // LSB
           } else {
               // Two-byte value
               uint16_t value = get_measurement_value();
               response[1] = (value >> 8) & 0xFF; // MSB
               response[2] = value & 0xFF; // LSB
           }

           // Add timestamp and origin
           uint32_t timestamp = get_current_timestamp();
           memcpy(&response[3], &timestamp, 4);
           response[7] = ORIGIN_WATCH;
       } else {
           // Set error flag
           response[0] |= 0x80; // Set error bit
           response[2] = get_error_code();
       }

       // Send response back to BLE module
       send_response_to_ble(response, sizeof(response));
   }
   ```

### 4.3 Response Processing

1. **STM32 sends response/indication**:
   - After completing the measurement, STM32 prepares a response packet
   - The packet structure follows a standardized format:
     - Header: Contains packet type, length, and addressing information
     - Payload: Contains the vital data or error information
     - Footer: Contains integrity check information
   - The packet includes:
     - Vital ID (which measurement was taken)
     - Measurement value (e.g., heart rate in BPM, temperature in degrees)
     - Timestamp (when the measurement was taken)
     - Origin information (which component requested the measurement)
     - Error flags (if any errors occurred)
   - STM32 sends this packet back to the BLE module through the Transport Manager
   - The Transport Manager receives the packet and verifies its integrity
   - It then forwards the packet to the Primitive Manager
   - The Primitive Manager identifies it as a response/indication
   - It extracts the vital data and calls `BLE_ExecuteIndication()`

2. **BLE_ExecuteIndication() function**:
   ```c
   enum exec_req BLE_ExecuteIndication(enum comm_subid sub_id, uint8_t* data, uint16_t len)
   {
       return sendOverBle(sub_id, data, len, Indication_Status);
   }
   ```
   - This function is called when an indication is received from STM32
   - It takes three parameters:
     - `sub_id`: The vital type (e.g., `comm_wHR`)
     - `data`: Pointer to the measurement data
     - `len`: Length of the data
   - It calls `sendOverBle()` to format and send the data to the client
   - The `Indication_Status` parameter indicates this is a response to a request
   - This function is part of the Upper Layer Interface that bridges the Communication Manager and BLE stack

3. **sendOverBle() function**:
   ```c
   enum exec_req sendOverBle(enum comm_subid sub_id, uint8_t *data, uint16_t len)
   {
       uint8_t gattPacket[25] = {0};
       uint8_t param_id, param_val_msb, param_val_lsb, gatt_handle = 0;

       switch (sub_id)
       {
           case comm_wHR:
               {
                   param_id = HEART_RATE;
                   if(data[PACKET_ORIGIN] & 0x80)
                   {
                       return awt_handle_error(param_id, data, len, gattPacket);
                   }
                   else
                   {
                       param_val_msb = 0;
                       param_val_lsb = data[PACKET_PAYLOAD];
                   }
                   gatt_handle = VITAL_VAL;
               }
               break;
           // Other vitals...
       }

       // Format and send the packet
       gattPacket[PARAM_ID] = param_id;
       gattPacket[PARAM_VAL_MSB] = param_val_msb;
       gattPacket[PARAM_VAL_LSB] = param_val_lsb;
       memcpy(&gattPacket[TIMESTAMP_3], &data[PACKET_TIMESTAMP], 4);
       gattPacket[ORIGIN_TYPE] = data[PACKET_ORIGIN] & 0x1F;

       return Send_To_Gatt_Client(gattPacket, 8, VITAL_VAL);
   }
   ```

   - This is the core function that formats the raw data for BLE transmission
   - It allocates a buffer (`gattPacket`) for the formatted data
   - It uses a switch statement to process different vital types
   - For each vital type:
     - It sets the `param_id` to the appropriate vital ID
     - It checks for error flags in the data (bit 7 of PACKET_ORIGIN)
     - If an error is detected, it calls `awt_handle_error()`
     - If no error, it extracts the measurement value
   - The data extraction varies by vital type:
     - Heart Rate: Single byte value (BPM)
     - Blood Oxygen: Single byte percentage
     - Body Temperature: Two bytes (MSB and LSB)
     - Blood Pressure: Two bytes (systolic and diastolic)
   - It formats a standardized 8-byte GATT packet:
     - Byte 0: Vital ID
     - Bytes 1-2: Measurement value (MSB and LSB)
     - Bytes 3-6: Timestamp
     - Byte 7: Origin information
   - It calls `Send_To_Gatt_Client()` to send the packet to the client device
   - The function handles special cases for each vital type:
     - For Heart Rate: Values typically range from 40-200 BPM
     - For Blood Oxygen: Values typically range from 90-100%
     - For Body Temperature: Values are in hundredths of degrees (e.g., 3720 for 37.20°C)
     - For Blood Pressure: First byte is systolic, second byte is diastolic

4. **Send_To_Gatt_Client() function**:
   ```c
   enum exec_req Send_To_Gatt_Client(uint8_t *Param, uint16_t Service_Len, uint8_t Handle_Value)
   {
       if (ke_state_get(TASK_APP) == APP_CONNECTED)
       {
           struct custs1_val_ind_req* req = KE_MSG_ALLOC_DYN(CUSTS1_VAL_IND_REQ,
                                                             prf_get_task_from_id(TASK_ID_CUSTS1),
                                                             TASK_APP,
                                                             custs1_val_ind_req,
                                                             Service_Len);
           // Get source ID
           Get_Indication_src_id(&src_id);
           uint8_t conidx = KE_IDX_GET(src_id);

           req->conidx = app_env[conidx].conidx;
           req->handle = Handle_Value;
           req->length = Service_Len;
           memcpy(req->value, Param, Service_Len);

           ke_msg_send(req);

           if(Param[0] != ACK_TO_APP){
               ResetCommLock();
           }

           return UL_C;
       }
       else{
           return UL_F;
       }
   }
   ```

   - This function interfaces with the BLE stack to send data to the client
   - It first checks if a BLE connection is active (`APP_CONNECTED` state)
   - If connected, it allocates a GATT indication request:
     - Uses `KE_MSG_ALLOC_DYN()` from the Dialog BLE stack
     - Specifies the destination task (CUSTS1 profile task)
     - Specifies the source task (APP task)
     - Allocates memory for the data
   - It retrieves the connection index using `Get_Indication_src_id()`
   - It sets up the request parameters:
     - `conidx`: Connection index
     - `handle`: GATT characteristic handle
     - `length`: Data length
     - `value`: Data value
   - It sends the request to the BLE stack using `ke_msg_send()`
   - It resets the communication lock to allow new requests (unless this is an acknowledgment)
   - It returns a success code (`UL_C`) if the operation was successful
   - The `Handle_Value` parameter determines which GATT characteristic will be used:
     - `VITAL_VAL`: For standard vital measurements (8 bytes)
     - `SVC1_ECG_1_VAL`: For ECG data (variable length)
     - `USR_NAME_1_VAL`: For user information

5. **Client receives the vital data**:
   - The BLE stack processes the indication request
   - It formats a BLE ATT indication packet according to the BLE specification
   - It sends the packet over the air to the client device
   - The client's BLE stack receives the indication and sends an acknowledgment
   - The client application is notified of the new data
   - The application extracts and processes the vital data:
     - Parses the packet format
     - Extracts the vital ID, value, timestamp, and origin
     - Displays the information to the user
     - May store the data for historical tracking
     - May perform additional analysis on the data
   - For ECG data, multiple indications may be sent in sequence to transfer the complete waveform
   - The client must reassemble these packets to reconstruct the full ECG waveform

### 4.4 Error Handling

1. **Error detection**:
   - Several types of errors can occur during the vital measurement process:
     - **Busy Error (ERR_VITAL_BUSY)**: The sensor is already performing a measurement
     - **Decline Error (ERR_VITAL_DECLINE)**: The measurement was declined (e.g., by user)
     - **Low Battery Error (ERR_VITAL_LBATT)**: Battery too low to perform measurement
     - **Measurement Error**: The sensor failed to get a valid reading
     - **Range Error**: The measurement is outside the expected range
   - If an error occurs during measurement, the STM32 sets the error flag (bit 7 of PACKET_ORIGIN)
   - The specific error type is encoded in the PACKET_PAYLOAD field
   - When `sendOverBle()` detects the error flag, it calls `awt_handle_error()` to process the error

2. **Error processing**:
   ```c
   enum exec_req awt_handle_error(uint8_t param_id, uint8_t *data, uint16_t len, uint8_t *gattPacket)
   {
       if((data[PACKET_PAYLOAD] == ERR_VITAL_DECLINE) || (data[PACKET_PAYLOAD] == ERR_VITAL_BUSY))
       {
           return hdlVitalUnservicable(param_id, data, len);
       }
       else if((data[PACKET_PAYLOAD] == ERR_VITAL_LBATT))
       {
           gattPacket[0] = 0x0A; // Watch status
           gattPacket[1] = 0;
           gattPacket[2] = 3; // LOW BATTERY
           memcpy(&gattPacket[TIMESTAMP_3], &data[PACKET_TIMESTAMP], 4);
           gattPacket[ORIGIN_TYPE] = data[PACKET_ORIGIN] & 0x1F;
           return Send_To_Gatt_Client(gattPacket, 8, VITAL_VAL);
       }
       else
       {
           // Map vital ID to error ID
           switch(param_id)
           {
               case HEART_RATE:
                   param_id = ERR_HR_ERROR;
                   break;
               // Other vitals...
           }
           return hdlVitalError(param_id, data, len);
       }
   }
   ```

   - This function is the central error handling mechanism for vital measurements
   - It categorizes errors into three main types:
     1. **Unserviceable Errors**: When the vital can't be measured due to busy or declined state
     2. **Low Battery Error**: When the battery is too low to perform the measurement
     3. **Measurement Errors**: When the sensor fails to get a valid reading
   - For each error type, it formats an appropriate error packet and sends it to the client
   - For measurement errors, it maps the vital ID to a specific error ID (e.g., HEART_RATE → ERR_HR_ERROR)

3. **Unserviceable vital handling**:
   ```c
   static inline enum exec_req hdlVitalUnservicable(uint8_t vital_id, uint8_t * data, uint16_t len)
   {
       uint8_t vitals_pkt[8];
       memset(vitals_pkt, 0, sizeof(vitals_pkt));

       vitals_pkt[UNSERICEABLE_ID_OFFSET] = 16; // Unservicable Id as per spec.
       vitals_pkt[UNSERICEABLE_VITAL_OFFSET] = vital_id;

       if(data[PACKET_PAYLOAD] == ERR_VITAL_BUSY)
           vitals_pkt[UNSERICEABLE_REASON_OFFSET] = iReading_inprogress;
       else if(data[PACKET_PAYLOAD] == ERR_VITAL_DECLINE)
       {
           // Set specific reason based on vital type
           if(vital_id == EKG)
           {
               vitals_pkt[UNSERICEABLE_REASON_OFFSET] = iRemote_Ekgreq_declined;
           }
           else if(vital_id == BLOOD_PRESSURE)
           {
               vitals_pkt[UNSERICEABLE_REASON_OFFSET] = iRemote_bpreq_declined;
           }
           else
           {
               vitals_pkt[UNSERICEABLE_REASON_OFFSET] = iMeasurement_failed;
           }
       }

       return Send_To_Gatt_Client(vitals_pkt, VITAL_CHAR_LEN, VITAL_VAL);
   }
   ```

   - This function handles the specific case of unserviceable vitals
   - It creates a standardized error packet with:
     - Error ID (16 for unserviceable)
     - Vital ID (which vital was requested)
     - Reason code (why the vital is unserviceable)
   - Different reason codes are used based on the vital type and error condition:
     - `iReading_inprogress`: When a measurement is already in progress
     - `iRemote_Ekgreq_declined`: When an ECG request is declined
     - `iRemote_bpreq_declined`: When a blood pressure request is declined
     - `iMeasurement_failed`: For other measurement failures
   - The error packet is sent to the client using the same GATT characteristic as normal vital data

## 5. Special Case: ECG Data

ECG data is handled differently due to its larger size and complexity:

1. **ECG request**:
   ```c
   void ECG_SAMPLES_Write_Handler(uint8_t *Ecg_Request)
   {
       if(*Ecg_Request == EKG)
       {
           notify_stm(COMMAND, comm_wECG, NULL, NULL);
       }
   }
   ```

   - The ECG request is initiated when a client writes to the ECG characteristic
   - The write handler checks if the request is for ECG data (EKG)
   - If so, it calls `notify_stm()` with the ECG command ID (`comm_wECG`)
   - This follows the same initial flow as other vitals

2. **ECG data acquisition**:
   - The STM32 receives the ECG request and activates the ECG sensors
   - ECG data is collected over a period of time (typically several seconds)
   - The data consists of multiple samples representing the electrical activity of the heart
   - The STM32 may also perform some initial processing on the raw data:
     - Filtering to remove noise
     - QRS detection to identify heartbeats
     - Basic analysis for rhythm abnormalities
   - The processed data is then prepared for transmission
   - Due to its size, ECG data is broken into multiple segments

3. **ECG data transmission**:
   - ECG data is sent in multiple segments using buffer transfer
   - Each segment contains a portion of the ECG waveform
   - The segments include sequence numbers to ensure proper ordering
   - A CRC (Cyclic Redundancy Check) is included to verify data integrity
   - The Communication Manager handles the segmentation and reassembly
   - Each segment is processed by `sendBuffOverBle()`

4. **ECG data formatting**:
   ```c
   enum exec_req sendBuffOverBle(enum comm_subid sub_id, uint8_t *data, uint16_t len, uint8_t seqNo, uint32_t crc)
   {
       switch (sub_id)
       {
           case comm_wECGSamples:
               {
                   if (len % 2 != 0) {
                       // Error if Odd length data
                       return UL_F;
                   }
                   for (uint16_t i = 0; i < len; i += 2) { //converting little endian to big endian for BLE
                       uint8_t temp = data[i];
                       data[i] = data[i + 1];
                       data[i + 1] = temp;
                   }
                   for(uint16_t i = 0; i < len; i++)
                       data[len - i - 1 + 5] = data[len - i -1];

                   data[0] = seqNo + 1;
                   data[1] = crc>>24;
                   data[2] = crc>>16 & 0xff;
                   data[3] = crc>>8 & 0xff;
                   data[4] = crc & 0xff;

                   return Send_To_Gatt_Client(data, len + 5, SVC1_ECG_1_VAL);
               }
       }
   }
   ```

   - This function formats ECG data for transmission over BLE
   - It performs several important operations:
     1. Validates that the data length is even (ECG samples are 16-bit values)
     2. Converts the data from little-endian to big-endian format for BLE transmission
     3. Rearranges the data to prepare it for transmission
     4. Adds a 5-byte header containing:
        - Sequence number (to track the order of segments)
        - 32-bit CRC (to verify data integrity)
     5. Sends the formatted data to the client using a dedicated ECG characteristic
   - The ECG data is sent using a different GATT characteristic (`SVC1_ECG_1_VAL`) than regular vitals
   - This allows for larger data transfers and specialized handling by the client

5. **Client processing of ECG data**:
   - The client receives multiple ECG data segments
   - It reassembles them based on the sequence numbers
   - It verifies the data integrity using the CRC
   - Once all segments are received, it can process and display the complete ECG waveform
   - The client may perform additional analysis on the ECG data

## 6. Data Packet Format

### 6.1 Standard Vital Data Packet (8 bytes)

| Byte | Field | Description |
|------|-------|-------------|
| 0 | PARAM_ID | Vital ID (e.g., HEART_RATE=0x01, BLOOD_OXYGEN=0x02) |
| 1 | PARAM_VAL_MSB | Most significant byte of value |
| 2 | PARAM_VAL_LSB | Least significant byte of value |
| 3-6 | TIMESTAMP | 4-byte timestamp (Unix time format) |
| 7 | ORIGIN_TYPE | Origin of the request (Watch=0x00, Scheduler=0x01, App=0x02) |

Examples:
- Heart Rate (72 BPM): `[0x01, 0x00, 0x48, <timestamp>, <origin>]`
- Blood Oxygen (98%): `[0x02, 0x00, 0x62, <timestamp>, <origin>]`
- Body Temperature (37.2°C): `[0x03, 0x01, 0x74, <timestamp>, <origin>]` (37.2°C = 0x0174 in hundredths of degrees)

### 6.2 Error Packet Format

| Byte | Field | Description |
|------|-------|-------------|
| 0 | ERR_NW_ID_OFFSET | Error ID (0x0E for VITAL_ERROR) |
| 1 | ERR_PARAM_ID | Vital ID that caused the error |
| 2 | ERR_ERR_NO | Error number |
| 3-6 | TIMESTAMP | 4-byte timestamp (Unix time format) |
| 7 | ORIGIN_TYPE | Origin of the request |

Error Numbers:
- 0x01: General measurement error
- 0x02: Out of range error
- 0xFD (253): Low battery error
- 0xFE (254): Vital declined
- 0xFF (255): Vital busy

### 6.3 Unserviceable Vital Packet Format

| Byte | Field | Description |
|------|-------|-------------|
| 0 | UNSERICEABLE_ID_OFFSET | Unserviceable ID (0x10 or 16) |
| 1 | UNSERICEABLE_VITAL_OFFSET | Vital ID that is unserviceable |
| 2 | UNSERICEABLE_REASON_OFFSET | Reason code |
| 3-6 | TIMESTAMP | 4-byte timestamp |
| 7 | ORIGIN_TYPE | Origin of the request |

Reason Codes:
- 0x01: Reading in progress
- 0x02: Remote ECG request declined
- 0x03: Remote BP request declined
- 0x04: Measurement failed

### 6.4 ECG Data Packet Format

| Bytes | Field | Description |
|-------|-------|-------------|
| 0 | Sequence Number | Segment sequence number (starting from 1) |
| 1-4 | CRC | 32-bit CRC for data integrity verification |
| 5+ | ECG Data | Variable length ECG sample data (16-bit samples) |

ECG data is sent in multiple packets, each containing a portion of the complete ECG waveform. The client must reassemble these packets based on the sequence numbers to reconstruct the full waveform.

## 7. Conclusion

### 7.1 Connection Lifecycle

The complete lifecycle of a BLE connection for vitals monitoring involves these key steps:

1. **Device Discovery and Connection**:
   - Client scans for and discovers the BLE device
   - Client establishes connection with the device

2. **Service Discovery and Configuration**:
   - Client discovers the custom service and characteristics
   - Client enables indications for the vital characteristics
   - Client may also configure user settings or preferences

3. **Vitals Requests and Responses**:
   - Client requests vitals by writing to the appropriate characteristic
   - Device processes the request through the Communication Manager and STM32
   - Device sends the measurement results back via indications
   - Client receives and processes the data

4. **Disconnection and Cleanup**:
   - When monitoring is complete, client may disconnect
   - Device cleans up resources and returns to advertising state
   - Client may reconnect later to resume monitoring

### 7.2 Summary

The vitals request flow involves multiple components working together to process the request, obtain the measurement, and deliver the result to the client. The flow includes error handling for various scenarios such as busy sensors, declined requests, and measurement failures.

The design allows for efficient communication between the BLE module and the STM32 co-processor, with the Communication Manager acting as the intermediary to ensure reliable data exchange.

### 7.3 Key Features of the Design

1. **Modular Architecture**:
   - Clear separation of concerns between BLE communication, sensor management, and data processing
   - Well-defined interfaces between components allow for independent development and testing
   - Extensible design that can accommodate new vital types or sensors

2. **Robust Error Handling**:
   - Comprehensive error detection and reporting
   - Different error types are handled appropriately
   - Error information is conveyed to the client in a standardized format

3. **Efficient Data Transfer**:
   - Standardized packet formats for different data types
   - Special handling for large data sets (like ECG)
   - Optimized for BLE's limited bandwidth

4. **Security and Reliability**:
   - Data integrity verification through CRC
   - Sequence numbering for multi-packet transfers
   - Connection state management to ensure data is only sent when a connection is active

### 7.4 Request Flow Summary

1. Client device requests a vital measurement through BLE GATT write
2. BLE module processes the request and forwards it to the Communication Manager
3. Communication Manager sends the request to the STM32 co-processor
4. STM32 performs the measurement using the appropriate sensor
5. Measurement result or error is sent back through the Communication Manager
6. BLE module formats the data and sends it to the client via GATT indication
7. Client receives and processes the data

This architecture provides a reliable and efficient mechanism for requesting and receiving vital sign measurements, with appropriate error handling and special cases for different vital types.
