#<SYMDEFS># ARM Linker, 5060061: Last Updated: Mon Aug 25 16:12:57 2025
0x00000000 N __ARM_use_no_argv
0x00000000 N _printf_a
0x00000000 N _printf_c
0x00000000 N _printf_charcount
0x00000000 N _printf_d
0x00000000 N _printf_e
0x00000000 N _printf_f
0x00000000 N _printf_flags
0x00000000 N _printf_fp_dec
0x00000000 N _printf_fp_hex
0x00000000 N _printf_g
0x00000000 N _printf_i
0x00000000 N _printf_int_dec
0x00000000 N _printf_l
0x00000000 N _printf_lc
0x00000000 N _printf_ll
0x00000000 N _printf_lld
0x00000000 N _printf_lli
0x00000000 N _printf_llo
0x00000000 N _printf_llu
0x00000000 N _printf_llx
0x00000000 N _printf_longlong_dec
0x00000000 N _printf_longlong_hex
0x00000000 N _printf_longlong_oct
0x00000000 N _printf_ls
0x00000000 N _printf_mbtowc
0x00000000 N _printf_n
0x00000000 N _printf_o
0x00000000 N _printf_p
0x00000000 N _printf_percent
0x00000000 N _printf_pre_padding
0x00000000 N _printf_return_value
0x00000000 N _printf_s
0x00000000 N _printf_sizespec
0x00000000 N _printf_str
0x00000000 N _printf_truncate_signed
0x00000000 N _printf_truncate_unsigned
0x00000000 N _printf_u
0x00000000 N _printf_wc
0x00000000 N _printf_wctomb
0x00000000 N _printf_widthprec
0x00000000 N _printf_x
0x000000a0 N __Vectors_Size
0x07f02001 T uECC_vli_add
0x07f02019 T uECC_vli_sub
0x07f02035 T uECC_vli_mult
0x07f02159 T set_system_clocks
0x07f0216b T set_peripheral_clocks
0x07f02183 T rf_workaround_init
0x07f02185 T get_stack_usage
0x07f0218d T rwip_eif_get_func
0x07f0219d T rwip_set_em_base
0x07f021a3 T platform_initialization
0x07f02255 T ble_init
0x07f022cb T ble_regs_push
0x07f02323 T ble_regs_pop
0x07f02379 T platform_sleep
0x07f02641 T rf_reinit
0x07f02649 T smpc_check_param
0x07f02653 T smpc_pdu_recv
0x07f0265d T lld_sleep_compensate
0x07f02667 T lld_sleep_init
0x07f02671 T lld_sleep_us_2_lpcycles
0x07f0267b T lld_sleep_lpcycles_2_us
0x07f02685 T uart_flow_off
0x07f0268d T uart_finish_transfers
0x07f02695 T uart_read
0x07f0269d T uart_write
0x07f026a5 T UART_Handler
0x07f026ad T uart_init
0x07f026b5 T uart_flow_on
0x07f026bd T gtl_init
0x07f026c5 T gtl_eif_init
0x07f026cd T gtl_eif_read_start
0x07f026d5 T gtl_eif_read_hdr
0x07f026dd T gtl_eif_read_payl
0x07f026e5 T gtl_eif_tx_done
0x07f026ed T gtl_eif_rx_done
0x07f026f5 T h4tl_init
0x07f026fd T h4tl_read_start
0x07f02705 T h4tl_read_hdr
0x07f0270d T h4tl_read_payl
0x07f02715 T h4tl_read_next_out_of_sync
0x07f0271d T h4tl_out_of_sync
0x07f02725 T h4tl_tx_done
0x07f0272d T h4tl_rx_done
0x07f02735 T ke_task_init
0x07f0273d T ke_timer_init
0x07f02745 T llm_encryption_done
0x07f0274d T nvds_get
0x07f02755 T nvds_del
0x07f0275d T nvds_put
0x07f02765 T rwip_eif_get
0x07f0276d T platform_reset
0x07f02777 T lld_test_stop
0x07f02781 T lld_test_mode_tx
0x07f0278b T lld_test_mode_rx
0x07f02795 T prf_init
0x07f0279f T prf_add_profile
0x07f027a9 T prf_create
0x07f027b3 T prf_cleanup
0x07f027bd T prf_get_id_from_task
0x07f027c7 T prf_get_task_from_id
0x07f027d1 T nvds_init
0x07f027d9 T SetSystemVars
0x07f027e3 T dbg_init
0x07f027ed T dbg_platform_reset_complete
0x07f027f7 T hci_rd_local_supp_feats_cmd_handler
0x07f02807 T l2cc_pdu_pack
0x07f0281d T l2cc_pdu_unpack
0x07f02835 T l2c_send_lecb_message
0x07f0283f T l2c_process_sdu
0x07f02849 T l2cc_pdu_recv_ind_handler
0x07f02859 T gapc_lecb_connect_cfm_handler
0x07f02869 T atts_l2cc_pdu_recv_handler
0x07f02873 T attc_l2cc_pdu_recv_handler
0x07f0287d T crypto_init
0x07f02887 T llm_le_adv_report_ind
0x07f02891 T PK_PointMult
0x07f0289b T llm_p256_start
0x07f028a5 T llm_create_p256_key
0x07f028b1 T llm_p256_req_handler
0x07f028c3 T llc_le_length_effective
0x07f028cf T llc_le_length_conn_init
0x07f028db T lld_data_tx_prog
0x07f028e7 T lld_data_tx_check
0x07f028f3 T llc_pdu_send
0x07f028ff T dia_rand
0x07f0290b T dia_srand
0x07f02917 T llc_data_notif
0x07f02931 T ba431_get_rand
0x07f0293d T smpc_public_key_exchange_start
0x07f02949 T smpc_dhkey_calc_ind
0x07f02955 T smpm_ecdh_key_create
0x07f02961 T ble_init_arp
0x07f02971 T co_buf_init
0x07f02a35 T co_buf_rx_free
0x07f02a4f T co_buf_rx_buffer_get
0x07f02a59 T co_buf_tx_buffer_get
0x07f02a81 T co_list_init
0x07f02a91 T co_list_pop_front
0x07f02ab3 T co_list_flush
0x07f02ac9 T co_list_push_back
0x07f02aed T co_list_pool_init
0x07f02b55 T co_list_push_front
0x07f02b71 T co_list_extract
0x07f02bc1 T co_list_find
0x07f02bd7 T co_list_merge
0x07f02bf3 T co_list_insert_before
0x07f02c2b T co_list_insert_after
0x07f02c67 T co_list_size
0x07f02c7b T co_bdaddr_compare
0x07f02c97 T co_array_reverse
0x07f02cb9 T llc_init
0x07f02ce5 T llc_common_nb_of_pkt_comp_evt_send
0x07f02d05 T llc_acl_tx_data_flush
0x07f02d6d T llc_stop
0x07f02db9 T llc_reset
0x07f02ddb T llc_le_length_effective_func
0x07f02e75 T llc_le_length_conn_init_func
0x07f02ed3 T llc_le_enh_con_cmp_evt_send
0x07f02ff3 T llc_le_con_cmp_evt_send
0x07f0308f T llc_start
0x07f03173 T llc_acl_tx_data_squash
0x07f03215 T llc_acl_tx_desc_flushed
0x07f03285 T llc_acl_tx_data_process
0x07f032d3 T llc_discon_event_complete_send
0x07f032f5 T llc_con_update_complete_send
0x07f0332f T llc_ltk_req_send
0x07f03367 T llc_feats_rd_event_send
0x07f033a3 T llc_version_rd_event_send
0x07f033d5 T llc_common_cmd_complete_send
0x07f033f3 T llc_common_cmd_status_send
0x07f0340f T llc_common_cmd_discard
0x07f03417 T llc_common_flush_occurred_send
0x07f03431 T llc_common_enc_key_ref_comp_evt_send
0x07f0344f T llc_common_enc_change_evt_send
0x07f034b7 T llc_con_update_ind
0x07f0355b T llc_lsto_con_update
0x07f03595 T llc_map_update_ind
0x07f03653 T llc_chnl_map_req_send
0x07f0366f T llc_add_bad_chnl
0x07f03709 T llc_pdu_send_func
0x07f0377b T llc_version_ind_pdu_send
0x07f037d5 T llc_ch_map_update_pdu_send
0x07f03825 T llc_pause_enc_req_pdu_send
0x07f03871 T llc_pause_enc_rsp_pdu_send
0x07f038d7 T llc_enc_req_pdu_send
0x07f039a3 T llc_enc_rsp_pdu_send
0x07f03a41 T llc_start_enc_rsp_pdu_send
0x07f03a99 T llc_reject_ind_pdu_send
0x07f03b37 T llc_con_update_pdu_send
0x07f03b8d T llc_con_param_req_pdu_send
0x07f03c1b T llc_con_param_rsp_pdu_send
0x07f03ca9 T llc_feats_req_pdu_send
0x07f03d09 T llc_start_enc_req_pdu_send
0x07f03dbd T llc_terminate_ind_pdu_send
0x07f03e31 T llc_unknown_rsp_send_pdu
0x07f03e65 T llc_length_req_pdu_send
0x07f03f31 T llc_length_rsp_pdu_send
0x07f03fa1 T llc_length_ind
0x07f0402d T llc_ping_req_pdu_send
0x07f0405d T llc_ping_rsp_pdu_send
0x07f0408d T llc_feats_req_ind
0x07f040f9 T llc_feats_rsp_ind
0x07f04167 T llc_vers_ind_ind
0x07f041fb T llc_terminate_ind
0x07f04237 T llc_pause_enc_req_ind
0x07f04267 T llc_pause_enc_rsp_ind
0x07f042dd T llc_enc_req_ind
0x07f0439d T llc_enc_rsp_ind
0x07f04449 T llc_start_enc_req_ind
0x07f044b7 T llc_start_enc_rsp_ind
0x07f0453f T llc_cntl_rcv
0x07f045cd T llcp_con_param_req_pdu_unpk
0x07f04645 T llcp_con_param_rsp_pdu_unpk
0x07f046bd T llc_con_update_req_ind
0x07f04717 T llc_ch_map_req_ind
0x07f04799 T llc_data_rcv
0x07f06bd1 T llc_util_get_free_conhdl
0x07f06c01 T llc_util_dicon_procedure
0x07f06c63 T llc_util_gen_skdx
0x07f06c77 T llc_util_update_channel_map
0x07f06c89 T llc_util_set_llcp_discard_enable
0x07f06ca1 T llc_util_set_auth_payl_to_margin
0x07f06cc9 T llm_add_bad_chnl
0x07f06d05 T llc_data_notif_func
0x07f06e11 T lld_init
0x07f06f5d T lld_reset
0x07f06fab T lld_adv_start
0x07f0710f T lld_adv_stop
0x07f07135 T lld_scan_start
0x07f0727d T lld_scan_stop
0x07f072b5 T lld_con_start
0x07f07647 T lld_move_to_master
0x07f076df T lld_con_update_req
0x07f0775d T lld_con_update_after_param_req
0x07f07949 T lld_con_param_rsp
0x07f07a45 T lld_con_param_req
0x07f07b1b T lld_con_stop
0x07f07b71 T lld_get_mode
0x07f07b95 T lld_move_to_slave
0x07f07d3b T lld_ch_map_ind
0x07f07d6b T lld_con_update_ind
0x07f07d79 T lld_crypt_isr
0x07f07d83 T lld_test_mode_tx_func
0x07f07e23 T lld_test_mode_rx_func
0x07f07eb5 T lld_test_stop_func
0x07f07f75 T lld_data_rx_check
0x07f07fb9 T lld_data_rx_flush
0x07f07fdf T lld_data_tx_check_func
0x07f08085 T lld_data_tx_loop
0x07f080b3 T lld_data_tx_push
0x07f08115 T lld_data_tx_prog_func
0x07f08255 T lld_data_tx_flush
0x07f08363 T lld_evt_drift_compute
0x07f08449 T lld_evt_elt_delete
0x07f08a6f T lld_evt_deffered_elt_handler
0x07f08b43 T lld_evt_init
0x07f08bb9 T lld_evt_init_evt
0x07f08bd7 T lld_evt_elt_insert
0x07f08c01 T lld_evt_conhdl2elt
0x07f08c1d T lld_evt_schedule_next
0x07f08d19 T lld_evt_schedule
0x07f08d55 T lld_evt_prevent_stop
0x07f08d57 T lld_evt_canceled
0x07f08d7b T lld_evt_scan_create
0x07f08e6f T lld_evt_move_to_master
0x07f08fef T lld_evt_update_create
0x07f090f1 T lld_evt_ch_map_update_req
0x07f09109 T lld_evt_move_to_slave
0x07f09347 T lld_evt_slave_update
0x07f09405 T lld_evt_adv_create
0x07f094b9 T lld_evt_end
0x07f095c1 T lld_evt_rx
0x07f095f3 T lld_evt_timer_isr
0x07f095fd T lld_evt_end_isr
0x07f0968b T lld_evt_rx_isr
0x07f09839 T lld_sleep_us_2_lpcycles_func
0x07f0985f T lld_sleep_lpcycles_2_us_func
0x07f09969 T lld_sleep_enter
0x07f099a9 T lld_sleep_wakeup
0x07f099c3 T lld_sleep_wakeup_end
0x07f099e9 T lld_wlcoex_connection_complete
0x07f09a01 T lld_wlcoex_remove_connection
0x07f09a15 T lld_wlcoex_set
0x07f09a29 T lld_util_get_bd_address
0x07f09a49 T lld_util_set_bd_address
0x07f09a85 T lld_util_freq2chnl
0x07f09aa7 T lld_util_get_local_offset
0x07f09ac1 T lld_util_get_peer_offset
0x07f09add T lld_util_connection_param_set
0x07f09b2d T llm_wl_clr
0x07f09b55 T llm_init
0x07f09d5b T llm_common_cmd_complete_send
0x07f09d73 T llm_ble_ready
0x07f09d79 T llm_wl_from_rl_restore
0x07f09df9 T llm_con_req_ind
0x07f0a0ef T llm_resolv_addr
0x07f0a131 T llm_util_rl_wl_update
0x07f0a16b T llm_alter_conn
0x07f0a207 T llm_adv_report_set
0x07f0a295 T llm_direct_adv_report_set
0x07f0a2d7 T llm_encryption_start
0x07f0a37f T llm_resolv_addr_inplace
0x07f0a42b T llm_le_adv_report_ind_func
0x07f0a93d T llm_con_req_tx_cfm
0x07f0aa59 T llm_common_cmd_status_send
0x07f0aa73 T llm_test_mode_start_tx
0x07f0ab8d T llm_test_mode_start_rx
0x07f0abcd T llm_set_adv_param
0x07f0ad33 T llm_gen_rand_addr
0x07f0adef T llm_wl_from_rl
0x07f0af1f T llm_set_adv_en
0x07f0b225 T llm_set_adv_data
0x07f0b2dd T llm_set_scan_rsp_data
0x07f0b3cd T llm_set_scan_param
0x07f0b453 T llm_set_scan_en
0x07f0b5a7 T llm_wl_dev_add
0x07f0b681 T llm_wl_dev_rem
0x07f0b6d5 T llm_create_con
0x07f0b9db T llm_encryption_done_func
0x07f0bcb3 T llm_get_chnl_assess_nb_pkt
0x07f0bcbb T llm_get_chnl_assess_nb_bad_pkt
0x07f0bcc3 T llm_get_min_rssi
0x07f0bccd T llm_le_scan_report_ind
0x07f0bd35 T llm_set_tx_oct_time
0x07f0bd5f T llm_p256_start_func
0x07f0bdd5 T llm_create_p256_key_func
0x07f0be85 T llm_p256_req_handler_func
0x07f0c6b9 T hci_rd_local_supp_feats_cmd_handler_func
0x07f0cf35 T llm_util_bd_addr_in_wl
0x07f0cfab T llm_util_check_address_validity
0x07f0cfbb T llm_util_check_map_validity
0x07f0d001 T llm_util_apply_bd_addr
0x07f0d019 T llm_util_set_public_addr
0x07f0d027 T llm_util_check_evt_mask
0x07f0d049 T llm_util_get_channel_map
0x07f0d057 T llm_util_get_supp_features
0x07f0d063 T llm_util_adv_data_update
0x07f0d087 T llm_util_bl_check
0x07f0d0c9 T llm_util_bl_add
0x07f0d11f T llm_util_bl_rem
0x07f0d16f T llm_util_rl_check
0x07f0d1a5 T llm_util_rl_add
0x07f0d223 T llm_util_rl_rem
0x07f0d249 T llm_util_rl_peer_find
0x07f0d275 T llm_util_rl_peer_resolv
0x07f0d2c7 T llm_util_rl_rpa_find
0x07f0d2f1 T PK_PointMult_func
0x07f0d401 T ea_time_get_slot_rounded
0x07f0d4cd T ea_init
0x07f0d521 T ea_elt_create
0x07f0d53b T ea_time_get_halfslot_rounded
0x07f0d56b T ea_elt_insert
0x07f0d7af T ea_elt_remove
0x07f0d837 T ea_elt_delete
0x07f0d851 T ea_interval_create
0x07f0d867 T ea_interval_insert
0x07f0d875 T ea_interval_delete
0x07f0d88f T ea_finetimer_isr
0x07f0d961 T ea_sw_isr
0x07f0d97f T ea_offset_req
0x07f0db3d T ea_sleep_check
0x07f0db93 T ea_interval_duration_req
0x07f0dcb3 T flash_identify
0x07f0dd01 T flash_init
0x07f0dd3d T flash_erase
0x07f0dda1 T flash_write
0x07f0de05 T flash_read
0x07f0de93 T uart_init_func
0x07f0def1 T uart_flow_on_func
0x07f0def9 T uart_flow_off_func
0x07f0df49 T uart_finish_transfers_func
0x07f0df61 T uart_read_func
0x07f0df77 T uart_write_func
0x07f0df99 T UART_Handler_func
0x07f0dfeb T uart_set_flow_off_retries_limit
0x07f0e045 T init_delay
0x07f0e047 T delay_us
0x07f0e319 T gtl_init_func
0x07f0e341 T gtl_enter_sleep
0x07f0e36b T gtl_exit_sleep
0x07f0e373 T gtl_send_msg
0x07f0e3fd T gtl_eif_read_start_func
0x07f0e41d T gtl_eif_read_hdr_func
0x07f0e43d T gtl_eif_read_payl_func
0x07f0e47b T gtl_eif_tx_done_func
0x07f0e48b T gtl_eif_rx_done_func
0x07f0e5bd T gtl_eif_init_func
0x07f0e5d7 T gtl_eif_write
0x07f0e5f9 T gtl_eif_start
0x07f0e603 T gtl_eif_stop
0x07f0e61f T gtl_env_curr_msg_type_set
0x07f0e8d3 T hci_tl_host_cmd_discarded
0x07f0e8f1 T hci_tl_send
0x07f0e939 T hci_tl_init
0x07f0e95d T hci_cmd_get_max_param_size
0x07f0e9a7 T hci_cmd_received
0x07f0eae3 T hci_acl_tx_data_alloc
0x07f0eb75 T hci_acl_tx_data_received
0x07f0ebdd T hci_acl_rx_data_alloc
0x07f0ebe9 T hci_acl_rx_data_received
0x07f0ec1f T hci_evt_received
0x07f0edcb T hci_tl_env_tx_queue_cnt_get
0x07f0eee3 T hci_util_pack
0x07f0efe3 T hci_util_unpack
0x07f0f4c9 T hci_look_for_cmd_desc
0x07f0f515 T hci_look_for_evt_desc
0x07f0f537 T hci_look_for_le_evt_desc
0x07f0f569 T hci_evt_mask_set
0x07f0f5b1 T hci_init
0x07f0f5cd T hci_reset
0x07f0f5e5 T hci_send_2_host
0x07f0f6cf T hci_host_cmd_discarded
0x07f0f6d7 T hci_send_2_controller
0x07f0f7b1 T h4tl_read_start_func
0x07f0f7cf T h4tl_read_hdr_func
0x07f0f7eb T h4tl_read_payl_func
0x07f0f805 T h4tl_read_next_out_of_sync_func
0x07f0f819 T h4tl_out_of_sync_func
0x07f0f83f T h4tl_out_of_sync_check
0x07f0f897 T h4tl_tx_done_func
0x07f0f8af T h4tl_rx_done_func
0x07f0f9f5 T h4tl_init_func
0x07f0fa11 T h4tl_write
0x07f0fa39 T h4tl_start
0x07f0fa41 T h4tl_stop
0x07f0fa55 T h4tl_env_rx_type_set
0x07f0fa61 T h4tl_env_hdr_set
0x07f0fa9d T attc_send_att_req
0x07f0fad9 T attc_allocate_att_req
0x07f0fafb T attc_send_hdl_cfm
0x07f0fb11 T attc_send_execute
0x07f0fb2b T attc_send_read_ind
0x07f1046b T attc_l2cc_pdu_recv_handler_func
0x07f104c5 T attm_convert_to128
0x07f104f5 T attm_uuid_comp
0x07f1054d T attm_uuid16_comp
0x07f10559 T attm_is_bt16_uuid
0x07f1057f T attm_is_bt32_uuid
0x07f1097f T attmdb_add_service
0x07f10a07 T attmdb_destroy
0x07f10a21 T attmdb_get_service
0x07f10a5f T attmdb_get_attribute
0x07f10a93 T attmdb_get_next_att
0x07f10af7 T attmdb_uuid16_comp
0x07f10b33 T attmdb_att_set_value
0x07f10bdf T attmdb_get_max_len
0x07f10c45 T attmdb_get_uuid
0x07f10d1b T attmdb_get_value
0x07f10e6b T attmdb_att_set_permission
0x07f10edd T attmdb_att_update_perm
0x07f10f4b T attmdb_svc_get_permission
0x07f10f69 T attmdb_att_get_permission
0x07f1106b T attmdb_svc_set_permission
0x07f1108f T attmdb_init
0x07f110a5 T attmdb_get_nb_svc
0x07f110b9 T attmdb_get_svc_info
0x07f110ed T attm_svc_create_db
0x07f111fb T attmdb_reserve_handle_range
0x07f1138d T atts_clear_read_cache
0x07f11519 T atts_send_error
0x07f11535 T atts_write_signed_cfm
0x07f1157b T atts_send_event
0x07f11603 T atts_clear_prep_data
0x07f11627 T atts_clear_rsp_data
0x07f1167d T atts_clear_pending_write_ind_data
0x07f116a1 T atts_write_rsp_send
0x07f122b9 T atts_l2cc_pdu_recv_handler_func
0x07f1252b T gattc_cleanup
0x07f125b5 T gattc_init
0x07f125e7 T gattc_update_state
0x07f1260b T gattc_create
0x07f1268b T gattc_con_enable
0x07f12691 T gattc_get_mtu
0x07f1269d T gattc_set_mtu
0x07f126e3 T gattc_get_requester
0x07f126ff T gattc_send_complete_evt
0x07f1275b T gattc_send_error_evt
0x07f12781 T gattc_get_operation
0x07f12797 T gattc_get_op_seq_num
0x07f127ad T gattc_get_operation_ptr
0x07f127b9 T gattc_set_operation_ptr
0x07f127c5 T gattc_reschedule_operation
0x07f12809 T gattc_reallocate_svc
0x07f13815 T gattm_svc_get_start_hdl
0x07f1381b T gattm_init
0x07f13839 T gattm_init_attr
0x07f1388d T gattm_create
0x07f13895 T gattm_cleanup
0x07f1389d T gattm_get_max_mtu
0x07f138a3 T gattm_set_max_mtu
0x07f138bf T gattm_get_max_mps
0x07f138c5 T gattm_set_max_mps
0x07f13b2d T l2cc_cleanup
0x07f13b71 T l2cc_init
0x07f13ba3 T l2cc_create
0x07f13bdb T l2cc_update_state
0x07f13d97 T hci_acl_data_rx_handler
0x07f14029 T l2cm_init
0x07f1403d T l2cm_create
0x07f14045 T l2cm_cleanup
0x07f1404d T l2cm_set_link_layer_buff_size
0x07f1405d T smpc_send_use_enc_block_cmd
0x07f14095 T smpc_send_start_enc_cmd
0x07f1410f T smpc_send_ltk_req_rsp
0x07f1416b T smpc_send_pairing_req_ind
0x07f1424f T smpc_send_pairing_ind
0x07f1436b T smpc_check_pairing_feat
0x07f14385 T smpc_launch_rep_att_timer
0x07f143c1 T smpc_check_repeated_attempts
0x07f14423 T smpc_check_max_key_size
0x07f14469 T smpc_check_key_distrib
0x07f144b3 T smpc_xor
0x07f144c9 T smpc_generate_l
0x07f14517 T smpc_generate_ci
0x07f1457b T smpc_generate_rand
0x07f145a1 T smpc_generate_e1
0x07f1465b T smpc_generate_cfm
0x07f146d5 T smpc_generate_stk
0x07f1472b T smpc_calc_subkeys
0x07f147a1 T smpc_clear_timeout_timer
0x07f147cb T smpc_pairing_end
0x07f14829 T smpc_tkdp_rcp_continue
0x07f148a1 T smpc_tkdp_rcp_start
0x07f148f5 T smpc_pdu_send
0x07f14993 T smpc_tkdp_send_start
0x07f14a1f T smpc_tkdp_send_continue
0x07f14a9b T smpc_get_key_sec_prop
0x07f14b67 T smpc_is_sec_mode_reached
0x07f14ba9 T smpc_handle_enc_change_evt
0x07f14c63 T smpc_pdu_recv_func
0x07f14ccf T smpc_generate_subkey
0x07f14d03 T leftshift_onebit
0x07f14d1b T padding
0x07f14d3f T smpc_generate_subkey_P2
0x07f14de3 T AES_CMAC_block
0x07f14ea3 T smpc_generate_f4
0x07f14fb7 T smpc_generate_g2
0x07f15061 T smpc_generate_f5
0x07f15073 T smpc_generate_f5_T
0x07f150e1 T smpc_generate_f5_P2
0x07f152b3 T smpc_generate_f6
0x07f1544d T smpm_send_encrypt_req
0x07f1547b T smpm_send_gen_rand_nb_req
0x07f15491 T smpm_check_addr_type
0x07f154fd T gapc_update_state
0x07f1552d T gapc_get_requester
0x07f15549 T gapc_send_complete_evt
0x07f15673 T gapc_init
0x07f156a5 T gapc_con_create
0x07f1575f T gapc_con_create_enh
0x07f15859 T gapc_con_cleanup
0x07f15869 T gapc_send_disconect_ind
0x07f1588b T gapc_get_conidx
0x07f158c5 T gapc_get_conhdl
0x07f158dd T gapc_get_role
0x07f158f9 T gapc_get_bdaddr
0x07f15919 T gapc_get_csrk
0x07f15937 T gapc_get_sign_counter
0x07f15955 T gapc_send_error_evt
0x07f15977 T gapc_get_operation
0x07f1598d T gapc_get_operation_ptr
0x07f15999 T gapc_set_operation_ptr
0x07f159a5 T gapc_reschedule_operation
0x07f159d5 T gapc_reschedule_conn_update
0x07f159fb T gapc_get_enc_keysize
0x07f15a13 T gapc_is_sec_set
0x07f15a9f T gapc_set_enc_keysize
0x07f15ab3 T gapc_link_encrypted
0x07f15acd T gapc_auth_set
0x07f15aed T gapc_svc_chg_ccc_get
0x07f15afd T gapc_svc_chg_ccc_set
0x07f15b13 T gapc_check_lecb_sec_perm
0x07f15b7b T gapc_search_lecb_channel
0x07f15bb5 T gapc_lecnx_check_tx
0x07f15bfd T gapc_lecnx_check_rx
0x07f15c41 T gapc_lecnx_get_field
0x07f15cb5 T gapc_process_op
0x07f15e2f T gapc_param_update_sanity
0x07f15e57 T gapc_param_cb_con_sanity
0x07f16323 T l2cc_pdu_recv_ind_handler_func
0x07f172eb T gapc_lecb_connect_cfm_handler_func
0x07f176c7 T gapm_init
0x07f17723 T gapm_init_attr
0x07f1774f T gapm_get_operation
0x07f17761 T gapm_get_requester
0x07f17779 T gapm_reschedule_operation
0x07f1779b T gapm_send_complete_evt
0x07f177d1 T gapm_send_error_evt
0x07f177f1 T gapm_con_create
0x07f17875 T gapm_con_enable
0x07f17881 T gapm_con_cleanup
0x07f178b1 T gapm_get_id_from_task
0x07f178f1 T gapm_get_task_from_id
0x07f1792d T gapm_is_disc_connection
0x07f18779 T gapm_adv_sanity
0x07f1886d T gapm_adv_op_sanity
0x07f189f3 T gapm_set_adv_mode
0x07f18a0d T gapm_set_adv_data
0x07f18a9d T gapm_execute_adv_op
0x07f18bc3 T gapm_scan_op_sanity
0x07f18ccb T gapm_set_scan_mode
0x07f18ce9 T gapm_execute_scan_op
0x07f18da3 T gapm_connect_op_sanity
0x07f18f23 T gapm_basic_hci_cmd_send
0x07f18f37 T gapm_execute_connect_op
0x07f190d9 T gapm_get_role
0x07f190e1 T gapm_get_ad_type_flag
0x07f19107 T gapm_add_to_filter
0x07f19187 T gapm_is_filtered
0x07f191eb T gapm_update_air_op_state
0x07f192b3 T gapm_get_irk
0x07f192b9 T gapm_get_bdaddr
0x07f192d5 T l2cc_pdu_pack_func
0x07f197d1 T l2cc_detect_dest
0x07f1982d T l2cc_handle_invalid_pdu
0x07f19943 T l2cc_pdu_unpack_func
0x07f19c5f T l2c_process_sdu_func
0x07f19d5f T l2c_send_lecb_message_func
0x07f19e59 T smpc_check_param_func
0x07f1aacd T gapc_hci_handler
0x07f1b745 T gapm_hci_handler
0x07f1b7b1 T smpc_pairing_start
0x07f1b837 T smpc_pairing_tk_exch
0x07f1b8f5 T smpc_pairing_ltk_exch
0x07f1b949 T smpc_pairing_csrk_exch
0x07f1b99f T smpc_pairing_rsp
0x07f1ba83 T smpc_pairing_req_handler
0x07f1babb T smpc_security_req_send
0x07f1bae5 T smpc_encrypt_start
0x07f1bb0b T smpc_encrypt_start_handler
0x07f1bb3d T smpc_encrypt_cfm
0x07f1bb69 T smpc_sign_command
0x07f1bc41 T smpc_sign_cont
0x07f1bdeb T smpc_calc_confirm_cont
0x07f1c32d T smpc_confirm_gen_rand
0x07f1c3f3 T smpc_public_key_exchange_start_func
0x07f1c417 T smpc_dhkey_calc_start
0x07f1c447 T smpc_sec_authentication_start
0x07f1c475 T smpc_dhkey_calc_ind_func
0x07f1c4b9 T smpm_gen_rand_addr
0x07f1c4d1 T smpm_resolv_addr
0x07f1c4f3 T smpm_use_enc_block
0x07f1c4fb T smpm_gen_rand_nb
0x07f1c503 T smpm_ecdh_key_create_func
0x07f1c521 T ke_init
0x07f1c553 T ke_flush
0x07f1c593 T ke_sleep_check
0x07f1c5a5 T ke_stats_get
0x07f1c5c1 T ke_event_init
0x07f1c5cd T ke_event_callback_set
0x07f1c5e1 T ke_event_set
0x07f1c60d T ke_event_clear
0x07f1c639 T ke_event_get
0x07f1c65f T ke_event_get_all
0x07f1c665 T ke_event_flush
0x07f1c66d T ke_event_schedule
0x07f1c6bd T ke_mem_init
0x07f1c709 T ke_mem_is_empty
0x07f1c749 T ke_check_malloc
0x07f1c7d9 T ke_malloc
0x07f1c8cf T ke_free
0x07f1c9b1 T ke_is_free
0x07f1c9c3 T ke_get_mem_usage
0x07f1c9cf T ke_get_max_mem_usage
0x07f1c9f5 T ke_msg_alloc
0x07f1ca2b T ke_msg_send
0x07f1ca57 T ke_msg_send_basic
0x07f1ca65 T ke_msg_forward
0x07f1ca6f T ke_msg_forward_new_id
0x07f1ca7f T ke_msg_free
0x07f1ca87 T ke_msg_dest_id_get
0x07f1ca8d T ke_msg_src_id_get
0x07f1ca93 T ke_msg_in_queue
0x07f1caa5 T ke_queue_extract
0x07f1caf5 T ke_queue_insert
0x07f1cddf T ke_task_init_func
0x07f1cdf3 T ke_task_create
0x07f1ce2b T ke_task_delete
0x07f1ce57 T ke_state_set
0x07f1ce81 T ke_state_get
0x07f1ce9f T ke_msg_discard
0x07f1cea3 T ke_msg_save
0x07f1cea7 T ke_task_msg_flush
0x07f1d067 T ke_timer_init_func
0x07f1d073 T ke_timer_set
0x07f1d107 T ke_timer_clear
0x07f1d15d T ke_timer_active
0x07f1d183 T ke_timer_sleep_check
0x07f1d289 T rwble_hl_init
0x07f1d2ab T rwble_hl_reset
0x07f1d2cd T rwble_hl_send_message
0x07f1d2d1 T rwip_check_wakeup_boundary
0x07f1d2f7 T rwip_init
0x07f1d3bb T rwip_reset
0x07f1d3f3 T rwip_version
0x07f1d3fb T rwip_schedule
0x07f1d4a7 T rwip_prevent_sleep_set
0x07f1d4c9 T rwip_wakeup
0x07f1d4df T rwip_prevent_sleep_clear
0x07f1d501 T rwip_wakeup_end
0x07f1d51d T rwip_wakeup_delay_set
0x07f1d52b T rwip_sleep_enable
0x07f1d531 T rwip_ext_wakeup_enable
0x07f1d555 T rwble_init
0x07f1d5bb T rwble_reset
0x07f1d5ef T rwble_version
0x07f1d61b T rwble_send_message
0x07f1d725 T YieldToScheduler
0x07f1d72d T xorshift64star
0x07f1d793 T uECC_set_rng
0x07f1d799 T uECC_get_rng
0x07f1d79f T uECC_curve_private_key_size
0x07f1d7af T uECC_curve_public_key_size
0x07f1d7b7 T uECC_vli_clear
0x07f1d7cd T uECC_vli_isZero
0x07f1d7ef T uECC_vli_testBit
0x07f1d801 T uECC_vli_numBits
0x07f1d83b T uECC_vli_set
0x07f1d879 T uECC_vli_equal
0x07f1d89d T uECC_vli_cmp
0x07f1d8d3 T uECC_vli_rshift1
0x07f1d8f1 T uECC_vli_square
0x07f1d8fd T uECC_vli_modAdd
0x07f1d92b T uECC_vli_modSub
0x07f1d94b T uECC_vli_mmod
0x07f1da55 T uECC_vli_modMult
0x07f1da77 T uECC_vli_modMult_fast
0x07f1da97 T uECC_vli_modSquare
0x07f1daa5 T uECC_vli_modSquare_fast
0x07f1dae5 T uECC_vli_modInv
0x07f1de21 T uECC_secp256r1
0x07f1e41f T uECC_vli_nativeToBytes
0x07f1e441 T uECC_vli_bytesToNative
0x07f1e47f T uECC_generate_random_int
0x07f1e4e1 T uECC_make_key
0x07f1e55f T uECC_shared_secret
0x07f1e61b T uECC_compress
0x07f1e649 T uECC_decompress
0x07f1e6b9 T uECC_valid_point
0x07f1e71b T uECC_valid_public_key
0x07f1e74f T uECC_compute_public_key
0x07f1e9d5 T uECC_sign
0x07f1eabb T uECC_sign_deterministic
0x07f1ec29 T uECC_verify
0x07f1eed5 T uECC_curve_num_words
0x07f1eedd T uECC_curve_num_bytes
0x07f1eee5 T uECC_curve_num_bits
0x07f1eeed T uECC_curve_num_n_words
0x07f1eefd T uECC_curve_num_n_bytes
0x07f1ef0d T uECC_curve_num_n_bits
0x07f1ef15 T uECC_curve_p
0x07f1ef19 T uECC_curve_n
0x07f1ef1d T uECC_curve_G
0x07f1ef21 T uECC_curve_b
0x07f1ef25 T uECC_vli_mod_sqrt
0x07f1ef2b T uECC_vli_mmod_fast
0x07f1ef31 T uECC_point_mult
0x07f1f005 T __aeabi_uidiv
0x07f1f005 T __aeabi_uidivmod
0x07f1f031 T __aeabi_idiv
0x07f1f031 T __aeabi_idivmod
0x07f1f059 T __aeabi_lmul
0x07f1f059 T _ll_mul
0x07f1f0d5 T rand
0x07f1f0e7 T srand
0x07f1f0f9 T __aeabi_memcpy
0x07f1f0f9 T __aeabi_memcpy4
0x07f1f0f9 T __aeabi_memcpy8
0x07f1f11d T __aeabi_memset
0x07f1f11d T __aeabi_memset4
0x07f1f11d T __aeabi_memset8
0x07f1f12b T __aeabi_memclr
0x07f1f12b T __aeabi_memclr4
0x07f1f12b T __aeabi_memclr8
0x07f1f12f T _memset$wrapper
0x07f1f141 T memcmp
0x07f1f15b T __aeabi_uread4
0x07f1f15b T __rt_uread4
0x07f1f15b T _uread4
0x07f1f16f T __aeabi_uwrite4
0x07f1f16f T __rt_uwrite4
0x07f1f16f T _uwrite4
0x07f1f181 T __aeabi_llsl
0x07f1f181 T _ll_shift_l
0x07f1f1a1 T __ARM_common_switch8
0x07f1f1bc D uart_api
0x07f1f1cc D co_sca2ppm
0x07f1f1dc D co_null_bdaddr
0x07f1f1e2 D co_default_bdaddr
0x07f1f468 D llc_state_handler
0x07f1f538 D llc_default_handler
0x07f1f556 D llm_debug_private_key
0x07f1f588 D llm_local_le_states
0x07f1f770 D llm_state_handler
0x07f1f7a0 D llm_default_handler
0x07f1f7a8 D LLM_AA_CT1
0x07f1f7ab D LLM_AA_CT2
0x07f1f7ad D ecc_p256_G
0x07f1f800 D gtl_default_state
0x07f1f808 D gtl_default_handler
0x07f1f814 D hci_cmd_desc_tab_lk_ctrl
0x07f1f838 D hci_cmd_desc_tab_ctrl_bb
0x07f1f8b0 D hci_cmd_desc_tab_info_par
0x07f1f8e0 D hci_cmd_desc_tab_stat_par
0x07f1f8ec D hci_cmd_desc_tab_le
0x07f1fb38 D hci_cmd_desc_tab_vs
0x07f1fc7c D rom_hci_cmd_desc_root_tab
0x07f1fcac D hci_evt_desc_tab
0x07f1fcf4 D hci_evt_le_desc_tab
0x07f1fd64 D attc_handlers
0x07f1fdd4 D atts_handlers
0x07f1fe54 D gattc_default_state
0x07f1ff34 D gattc_default_handler
0x07f1ff80 D gattm_default_state
0x07f1ffd8 D gattm_default_handler
0x07f1fff0 D l2cc_default_state
0x07f20008 D l2cc_default_handler
0x07f20029 D const_Rb
0x07f20039 D const_Zero
0x07f2005c D gapc_default_state
0x07f201ac D gapc_default_handler
0x07f20248 D gapm_default_state
0x07f20330 D gapm_default_handler
0x07f20338 D l2cc_connor_pkt_format
0x07f20340 D l2cc_signaling_pkt_format
0x07f2039c D l2cc_security_pkt_format
0x07f203d8 D l2cc_attribute_pkt_format
0x07f20454 D smpc_construct_pdu
0x07f20be5 T arch_printf_flush
0x07f20c9d T arch_vprintf
0x07f20cfd T arch_printf
0x07f20d11 T arch_puts
0x07f20d21 T arch_printf_process
0x07f20dcd T nvds_get_func
0x07f20ea9 T nvds_init_func
0x07f20ead T nvds_del_func
0x07f20eb1 T nvds_put_func
0x07f20f49 T csprng_seed
0x07f20f79 T csprng_get_next_uint32
0x07f21021 T trng_acquire
0x07f210d1 T prf_add_profile_func
0x07f211b1 T prf_cleanup_func
0x07f211f1 T prf_env_get
0x07f2121d T prf_src_task_get
0x07f2122d T prf_dst_task_get
0x07f21241 T prf_get_id_from_task_func
0x07f21279 T prf_get_task_from_id_func
0x07f212b1 T prf_reset_func
0x07f212fd T prf_itf_get
0x07f21321 T prf_pack_char_pres_fmt
0x07f2133f T prf_pack_date_time
0x07f2135f T prf_unpack_date_time
0x07f21381 T diss_compute_cfg_flag
0x07f21453 T diss_handle_to_value
0x07f21483 T diss_value_to_handle
0x07f214b7 T diss_check_val_len
0x07f214ed T diss_prf_itf_get
0x07f2184b T bass_get_att_handle
0x07f21901 T bass_get_att_idx
0x07f2196b T bass_exe_operation
0x07f21a6d T bass_prf_itf_get
0x07f22059 T suotar_prf_itf_get
0x07f22355 T check_client_char_cfg
0x07f2237f T get_value_handle
0x07f223cb T get_cfg_handle
0x07f2242d T custs1_get_att_handle
0x07f22449 T custs1_get_att_idx
0x07f22621 T custs1_prf_itf_get
0x07f226d3 T custs1_init_ccc_values
0x07f2270b T custs1_set_ccc_value
0x07f22823 T gattc_cmp_evt_handler
0x07f22837 T custs1_val_set_req_handler
0x07f22857 T custs1_val_ntf_req_handler
0x07f228b3 T custs1_val_ind_req_handler
0x07f2290f T custs1_att_info_rsp_handler
0x07f2294b T gattc_read_req_ind_handler
0x07f22b57 T gattc_att_info_req_ind_handler
0x07f22b99 T custs1_value_req_rsp_handler
0x07f22c19 T attm_svc_create_db_128
0x07f23085 T gapm_adv_report_ind_handler_ROM
0x07f2309f T gapc_security_ind_handler_ROM
0x07f23185 T gapc_set_dev_info_req_ind_handler_ROM
0x07f231c7 T gapm_profile_added_ind_handler_ROM
0x07f231f9 T gapc_param_update_req_ind_handler_ROM
0x07f23239 T gapc_le_pkt_size_ind_handler_ROM
0x07f23253 T gattc_svc_changed_cfg_ind_handler_ROM
0x07f2326f T gapc_peer_features_ind_handler_ROM
0x07f232a9 T app_entry_point_handler
0x07f232f1 T app_std_process_event
0x07f23335 T app_get_address_type_ROM
0x07f23361 T app_fill_random_byte_array_ROM
0x07f233f3 T __aeabi_ldivmod
0x07f2343f T __aeabi_llsr
0x07f2343f T _ll_ushift_r
0x07f23461 T __aeabi_uldivmod
0x07f234c1 T app_db_init_start
0x07f234dd T app_db_init
0x07f234e9 T app_easy_gap_confirm
0x07f23515 T append_device_name
0x07f23539 T app_easy_gap_update_adv_data
0x07f23581 T app_easy_gap_disconnect
0x07f235bd T app_easy_gap_advertise_stop
0x07f235d9 T active_conidx_to_conhdl
0x07f23605 T active_conhdl_to_conidx
0x07f23641 T app_timer_set
0x07f2365d T app_easy_gap_set_data_packet_length
0x07f23699 T get_user_prf_srv_perm
0x07f236c1 T app_set_prf_srv_perm
0x07f236f1 T prf_init_srv_perm
0x07f23715 T app_gattc_svc_changed_cmd_send
0x07f239e4 D blank_otp_bdaddr
0x07f23f58 D app_default_handler
0x07f23f60 D gap_cfg_user_var_struct
0x07fc0000 D __Vectors
0x07fc00a0 D __Vectors_End
0x07fc00a0 D booter_val
0x07fc0111 T __main
0x07fc0111 T _main_stk
0x07fc0115 T _main_scatterload
0x07fc0119 T __main_after_scatterload
0x07fc0119 T _main_clock
0x07fc0119 T _main_cpp_init
0x07fc0119 T _main_init
0x07fc0121 T __rt_final_cpp
0x07fc0121 T __rt_final_exit
0x07fc0125 T SystemInit
0x07fc01bd T Reset_Handler
0x07fc01f5 T SVC_Handler
0x07fc01f7 T PendSV_Handler
0x07fc01f9 T SysTick_Handler
0x07fc01fb T BLE_RF_DIAG_Handler
0x07fc01fb T DMA_Handler
0x07fc01fb T I2C_Handler
0x07fc01fb T KEYBRD_Handler
0x07fc01fb T RESERVED21_Handler
0x07fc01fb T RESERVED22_Handler
0x07fc01fb T RESERVED23_Handler
0x07fc01fb T RFCAL_Handler
0x07fc01fb T RTC_Handler
0x07fc01fb T SPI_Handler
0x07fc01fb T SWTIM1_Handler
0x07fc01fb T SWTIM_Handler
0x07fc01fb T WKUP_QUADEC_Handler
0x07fc01fb T XTAL32M_RDY_Handler
0x07fc020d T HardFault_HandlerC
0x07fc0225 T NMI_HandlerC
0x07fc0255 T main
0x07fc0565 T crypto_init_func
0x07fc0567 T ba431_get_rand_func
0x07fc057f T dia_rand_func
0x07fc0587 T dia_srand_func
0x07fc058f T dbg_init_func_empty
0x07fc0591 T dbg_platform_reset_complete_func_empty
0x07fc0593 T smpm_ecdh_key_create_func_empty
0x07fc0595 T platform_reset_func
0x07fc05a9 T arch_disable_sleep
0x07fc05c3 T arch_set_extended_sleep
0x07fc05f1 T arch_set_sleep_mode
0x07fc0609 T arch_get_sleep_mode
0x07fc0623 T arch_ble_ext_wakeup_get
0x07fc0629 T arch_ble_force_wakeup
0x07fc0669 T read_rcx_freq
0x07fc06b9 T calibrate_rcx20
0x07fc06d5 T init_pwr_and_clk_ble
0x07fc0775 T lld_sleep_lpcycles_2_us_rcx_func
0x07fc0795 T lld_sleep_lpcycles_2_us_sel_func
0x07fc0797 T lld_sleep_us_2_lpcycles_rcx_func
0x07fc07ab T lld_sleep_us_2_lpcycles_sel_func
0x07fc07ad T set_sleep_delay
0x07fc07ef T conditionally_run_radio_cals
0x07fc0875 T app_use_lower_clocks_check
0x07fc0879 T lld_sleep_init_func
0x07fc0893 T set_xtal32m_trim_value
0x07fc08e5 T system_init
0x07fc0b51 T arch_rom_init
0x07fc0cad T hash
0x07fc0cf9 T otp_cs_store
0x07fc0ecd T otp_cs_load_pd_rad
0x07fc0f2b T otp_cs_load_pd_adpll
0x07fc0f71 T otp_cs_get_adc_single_ge
0x07fc0f79 T otp_cs_get_adc_single_offset
0x07fc0f81 T otp_cs_get_adc_diff_ge
0x07fc0f89 T otp_cs_get_adc_diff_offset
0x07fc0f91 T otp_cs_get_adc_trim_val
0x07fc0f97 T otp_cs_get_adc_25_cal
0x07fc0f9d T otp_cs_get_xtal32m_trim_value
0x07fc0fa3 T otp_cs_get_xtal_wait_trim
0x07fc0ff5 T otp_hdr_get_bd_address
0x07fc1005 T syscntl_use_lowest_amba_clocks
0x07fc101b T syscntl_use_highest_amba_clocks
0x07fc1031 T syscntl_cfg_xtal32m_amp_reg
0x07fc107d T syscntl_dcdc_get_level
0x07fc1091 T syscntl_dcdc_set_level
0x07fc10b5 T syscntl_dcdc_turn_on_in_boost
0x07fc111d T GPIO_init
0x07fc111f T GPIO_SetPinFunction
0x07fc1131 T GPIO_SetInactive
0x07fc113f T GPIO_SetActive
0x07fc114d T GPIO_ConfigurePin
0x07fc1173 T GPIO_GetPinStatus
0x07fc1187 T GPIO_EnableIRQ
0x07fc126d T GPIO_ResetIRQ
0x07fc128d T GPIO_RegisterCallback
0x07fc12a1 T GPIOn_Handler
0x07fc12e5 T GPIO0_Handler
0x07fc12e9 T GPIO1_Handler
0x07fc12ed T GPIO2_Handler
0x07fc12f1 T GPIO3_Handler
0x07fc12f5 T GPIO4_Handler
0x07fc1367 T hw_otpc_enter_mode
0x07fc13b3 T hw_otpc_is_dcdc_reserved
0x07fc13b9 T hw_otpc_clear_dcdc_reserved
0x07fc13c1 T hw_otpc_set_speed
0x07fc13d1 T hw_otpc_init
0x07fc1431 T hw_otpc_disable
0x07fc14f9 T rbuf_get
0x07fc15eb T enableBreak
0x07fc15f9 T UART_Handler_SDK_func
0x07fc15fd T UART2_Handler
0x07fc1601 T uart_enable
0x07fc1631 T uart_disable
0x07fc1665 T uart_baudrate_setf
0x07fc1697 T uart_initialize
0x07fc174f T uart_write_byte
0x07fc175d T uart_write_buffer
0x07fc177b T uart_send
0x07fc18a5 T init_rand_seed_from_trng
0x07fc1921 T adc_temp_sensor_disable
0x07fc192f T adc_temp_sensor_enable
0x07fc1945 T adc_set_diff_input
0x07fc1953 T adc_set_se_input
0x07fc1a51 T adc_enable
0x07fc1a5d T adc_init
0x07fc1a93 T adc_start
0x07fc1a9f T ADC_Handler
0x07fc1ab1 T adc_get_temp
0x07fc1b29 T lld_sleep_compensate_func
0x07fc1b6d T power_up
0x07fc1bad T BLE_WAKEUP_LP_Handler
0x07fc1be7 T rwble_isr
0x07fc1ce5 T patched_ble_regs_push
0x07fc1cf5 T patched_ble_regs_pop
0x07fc1d05 T rwip_sleep
0x07fc1ed1 T rf_reg_rd
0x07fc1ed5 T rf_reg_wr
0x07fc1f81 T rf_init_func
0x07fc201f T rf_reinit_func
0x07fc2077 T ble_init_arp_func
0x07fc209b T rf_recalibration
0x07fc21b9 T set_recommended_settings
0x07fc2353 T dis_adpll
0x07fc2381 T kdtc_cal_end
0x07fc23cb T dis_kdtc_cal
0x07fc23d7 T en_adpll_tx
0x07fc2411 T dis_hclk
0x07fc2427 T en_kdtc_cal_mod0
0x07fc243d T kdtc_cal_init
0x07fc249f T kdtc_calibration
0x07fc2631 T rf_calibration
0x07fc2633 T rf_power_up
0x07fc2635 T rf_adplldig_activate
0x07fc265b T rf_adplldig_deactivate
0x07fc2667 T rf_pa_pwr_set
0x07fc266d T rf_pa_pwr_get
0x07fc2673 T rf_nfm_is_enabled
0x07fc27d5 T prf_init_func
0x07fc2835 T prf_create_func
0x07fc2875 T default_app_on_init
0x07fc2887 T default_app_on_connection
0x07fc28a3 T default_app_on_set_dev_config_complete
0x07fc28b3 T default_app_on_db_init_complete
0x07fc28bb T default_app_on_get_dev_name
0x07fc28cd T default_app_on_get_dev_appearance
0x07fc28d5 T default_app_on_get_dev_slv_pref_params
0x07fc28e7 T default_app_on_set_dev_info
0x07fc2915 T default_app_update_params_request
0x07fc2921 T default_app_generate_unique_static_random_addr
0x07fc29a5 T default_app_on_pairing_request
0x07fc29ad T default_app_on_csrk_exch
0x07fc29b5 T default_app_on_ltk_exch
0x07fc29cd T default_app_on_encrypt_req_ind
0x07fc29fb T default_app_on_addr_solved_ind
0x07fc2a2b T default_app_on_addr_resolve_failed
0x07fc2a33 T default_app_on_ral_cmp_evt
0x07fc2a89 T app_db_init_next
0x07fc2b2d T app_prf_enable
0x07fc2d53 T app_init
0x07fc2daf T app_easy_gap_undirected_advertise_get_active
0x07fc2db1 T app_easy_gap_undirected_advertise_start
0x07fc2dcb T app_easy_gap_param_update_start
0x07fc2de3 T app_easy_gap_dev_configure
0x07fc3083 T app_gap_process_handler
0x07fc30cf T app_sec_gen_ltk
0x07fc3105 T app_sec_gen_csrk
0x07fc3267 T app_sec_process_handler
0x07fc3295 T app_dis_init
0x07fc3297 T app_diss_create_db
0x07fc3379 T app_diss_process_handler
0x07fc33fd T app_check_BLE_active
0x07fc345b T app_easy_security_send_pairing_rsp
0x07fc3499 T app_easy_security_tk_exch
0x07fc3519 T app_easy_security_csrk_exch
0x07fc355d T app_easy_security_ltk_exch
0x07fc3575 T app_easy_security_set_ltk_exch_from_sec_env
0x07fc35cf T app_easy_security_encrypt_cfm
0x07fc35e7 T app_easy_security_set_encrypt_req_valid
0x07fc360b T app_easy_security_set_encrypt_req_invalid
0x07fc3617 T app_easy_security_accept_encryption
0x07fc363d T app_easy_security_reject_encryption
0x07fc3653 T app_easy_security_bdb_init
0x07fc365b T app_easy_security_bdb_add_entry
0x07fc3663 T app_easy_security_bdb_search_entry
0x07fc36bb T app_timer_api_process_handler
0x07fc3793 T app_easy_timer
0x07fc37e3 T app_easy_timer_cancel
0x07fc3823 T app_easy_timer_modify
0x07fc3915 T custs_get_func_callbacks
0x07fc3937 T app_custs1_create_db
0x07fc3975 T app_custs1_process_handler
0x07fc398d T default_app_bdb_init
0x07fc399f T default_app_bdb_add_entry
0x07fc3a63 T default_app_bdb_search_entry
0x07fc3ae5 T app_fill_random_byte_array
0x07fc3b17 T periph_init
0x07fc3b89 T Update_Ind_srcid
0x07fc3b8f T Get_Software_Revision
0x07fc3b93 T notify_stm
0x07fc3bd5 T ECG_SAMPLES_Write_Handler
0x07fc3be7 T PPG_SAMPLES_Write_Handler
0x07fc3be9 T USER_INFO_SENDING_Function
0x07fc3c17 T Send_To_Gatt_Client
0x07fc3c7f T update_alert_data
0x07fc3ca7 T UpdateVitalsReq
0x07fc3d41 T GetBondInfo
0x07fc3d47 T handle_Ind_Cfm
0x07fc3d8d T UpdateBleMacId
0x07fc3e63 T sendOverBle
0x07fc4005 T sendBuffOverBle
0x07fc4185 T user_app_init
0x07fc41e1 T user_app_on_pairing_succeeded
0x07fc41f9 T user_app_adv_start
0x07fc422d T user_app_connection
0x07fc4281 T user_app_adv_undirect_complete
0x07fc4289 T user_app_disconnect
0x07fc42b5 T user_app_on_tk_exch
0x07fc4339 T user_catch_rest_hndl
0x07fc44f9 T RingBuf_Init
0x07fc4509 T RingBuf_put
0x07fc4533 T RingBuf_get
0x07fc4565 T comm_init
0x07fc4589 T comm_manager
0x07fc45a9 T triggerCommMngr
0x07fc45cb T crTimeoutCallback
0x07fc45f5 T iTimeoutCallback
0x07fc461f T startITimer
0x07fc4647 T stopITimer
0x07fc465d T startCRTimer
0x07fc4683 T stopCRTimer
0x07fc4699 T triggerFromPrimitive
0x07fc46c5 T IsComMessageEmpty
0x07fc46c9 T Comm_Can_Sleep
0x07fc46cb T Comm_Sleep_Set
0x07fc4721 T sendBreakCmd
0x07fc4723 T WirelessUartTx
0x07fc4733 T COM_STWakeup
0x07fc474b T uart_trigger
0x07fc47a7 T wakeupWireless
0x07fc47d3 T Open_Master_tPort
0x07fc4819 T Open_Master_rPort
0x07fc4847 T Open_Slave_Port
0x07fc48b3 T hClose_Port
0x07fc48bf T Close_Port
0x07fc48e7 T Reset_Port
0x07fc4907 T get_crc
0x07fc493b T EnableRFSwitch
0x07fc4947 T _platform_transmit
0x07fc4955 T _platform_break_handle
0x07fc49f1 T getMtuSize
0x07fc49f5 T getRequesterTimeStmap
0x07fc49f9 T getCommRequester
0x07fc49fd T CmdIsServicable
0x07fc4a01 T IndIsServicable
0x07fc4a05 T MakePacket
0x07fc4a3d T BLE_ExecuteCommand
0x07fc4a49 T BLE_ExecuteIndication
0x07fc4a53 T BLE_ExecuteResponse
0x07fc4a5d T BLE_ExecuteBufferxfer
0x07fc4a7f T updateGattBuffer
0x07fc4a91 T COM_RXEvent
0x07fc4aa3 T COM_TXCEvent
0x07fc4ab5 T COM_BreakEvent
0x07fc4b51 T Com_Get_Data
0x07fc4b6d T Com_Send_Data
0x07fc4baf T RunMainLoop
0x07fc4bfd T WriteCmd
0x07fc4d2d T resetPrimitiveManagerCR
0x07fc51a3 T CopyTrigger
0x07fc5587 T PrimitiveMain
0x07fc55d9 T UpdatePrimitiveTriggerRetry
0x07fc5601 T SetPrimitiveTrigger_Data
0x07fc565b T SetPrimitiveTrigger_UL
0x07fc56a9 T SetPrimitiveTrigger_RT
0x07fc5701 T GetPrimitiveTrigger
0x07fc5785 T resetPrimitiveManagerInd
0x07fc57d5 T resetPrimitiveManager
0x07fc57e3 T resetPrimitiveMaster
0x07fc583d T InitQueue
0x07fc58a3 T enqueueCmdTxToFront
0x07fc58bf T enqueueIndTxToFront
0x07fc58df T isCmdTxFull
0x07fc58fd T isIndTxFull
0x07fc591d T isCmdTxEmpty
0x07fc593b T isIndTxEmpty
0x07fc595b T enqueueCmdTx
0x07fc59b3 T enqueueIndTx
0x07fc5a0d T dequeueCmdTx
0x07fc5a89 T dequeueIndTx
0x07fc5de3 T IsTransportMaster
0x07fc5e67 T resetTransportManager
0x07fc5f1b T GetTManagerContext
0x07fc62c9 T GetTransportTrigger
0x07fc62db T SetTransportTrigger
0x07fc62e9 T TransportMain
0x07fc631d T GetTransportManagerState
0x07fc6331 T GetBufferXferContext
0x07fc63b5 T InitTriggerQueue
0x07fc63e9 T isTriggerFull
0x07fc6407 T isTriggerEmpty
0x07fc641d T enqueueTrigger
0x07fc646f T dequeueTrigger
0x07fc64cd T JT_lld_test_mode_rx_func
0x07fc655b T JT_l2cc_pdu_unpack_func
0x07fc686d T JT_l2cc_pdu_recv_ind_handler_func
0x07fc68b5 T patch_func
0x07fc68bd T patch_global_vars_init
0x07fc68f5 T strlen
0x07fc6905 T __scatterload
0x07fc6905 T __scatterload_rt2
0x07fc6929 T __0sprintf$1
0x07fc6929 T __1sprintf$1
0x07fc6929 T __2sprintf
0x07fc6951 T __ARM_common_ll_muluu
0x07fc6981 T __scatterload_copy
0x07fc698f T __scatterload_null
0x07fc6991 T __scatterload_zeroinit
0x07fc6b08 D rom_nvds_cfg
0x07fc6b10 D rom_func_addr_table_var
0x07fc6c8c D rom_cfg_table_var
0x07fc6d80 D my_custom_msg_handlers
0x07fc6dec D arp_init_table
0x07fc6e5c D rom_cust_prf_cfg
0x07fc6e6c D custs1_default_state
0x07fc6eb4 D custs1_default_handler
0x07fc6ebc D prf_if
0x07fc6ed4 D rom_prf_cfg
0x07fc6f28 D prf_funcs
0x07fc7098 D app_process_handlers
0x07fc70b0 D rom_app_task_cfg
0x07fc70d8 D cust_prf_funcs
0x07fc71a8 D custs1_services
0x07fc71ad D custs1_services_size
0x07fc71b0 D custs1_att_db
0x07fc7348 D comm_default_state
0x07fc7350 D comm_handler
0x07fc74f4 D pa_en_dcf_tx_setting
0x07fc74f6 D ADVERTISEMENT_DURATION
0x07fc755d D device_config
0x07fc7820 D otp_hdr_timestamp
0x07fc7824 D twirq_set_value
0x07fc7828 D twirq_reset_value
0x07fc7830 D syscntl_dcdc_state
0x07fc7c74 D twext_value
0x07fc7c7c D calibration_res
0x07fc7ca6 D Comm_Lock
0x07fc7ca8 D Indication_Src_ID
0x07fc7caa D BLE_MAC_ID
0x07fc7dbd D currentState
0x07fc7dbe D prevState
0x07fc8688 D __initial_sp
0x07fc8688 D rwip_heap_non_ret
0x07fc8bd0 D sys_startup_flag
0x07fc8bd1 D last_temp
0x07fc8bd2 D rcx_cal_in_progress
0x07fc8bd4 D clk_freq_trim_reg_value
0x07fc8bd6 D arch_wkupct_deb_time
0x07fc8bd8 D xtal_wait_trim
0x07fc8be0 D last_temp_time
0x07fc8be4 D lp_clk_sel
0x07fc8be8 D rcx_freq
0x07fc8bec D rcx_period
0x07fc8bf0 D dev_bdaddr
0x07fc8bf8 D rcx_slot_duration
0x07fc8c00 D heap_log
0x07fc8c60 D GPIOHandlerFunction
0x07fc8c7c D arch_rwble_last_event
0x07fc8c80 D ble_finetim_corr
0x07fc8c84 D slp_period_retained
0x07fc8c88 D retained_BLE_EM_BASE_REG
0x07fc8c94 D prf_env
0x07fc8ce2 D app_env
0x07fc8cee D device_info
0x07fc8d12 D app_prf_srv_perm
0x07fc8d26 D app_state
0x07fc8d28 D app_sec_env
0x07fc8e93 D app_connection_idx
0x07fc8e94 D app_adv_data_update_timer_used
0x07fc8e95 D app_param_update_request_timer_used
0x07fc8e96 D mnf_data_index
0x07fc8e97 D stored_adv_data_len
0x07fc8e98 D stored_scan_rsp_data_len
0x07fc8e99 D mnf_data
0x07fc8e9f D stored_adv_data
0x07fc8ebe D stored_scan_rsp_data
0x07fc8edd D comm_state
0x07fc8ee0 D rwip_heap_db_ret
0x07fc92ec D rwip_heap_env_ret
0x07fc95a4 D rwip_heap_msg_ret
0x07fc9c00 D dummy
0x07fcb89c D trng_state_val
0x07fcb900 D ble_wakeup_executed
0x07fcb901 D rf_in_sleep
0x07fcb904 D custom_preinit
0x07fcb908 D custom_postinit
0x07fcb90c D custom_appinit
0x07fcb910 D custom_preloop
0x07fcb914 D custom_preschedule
0x07fcb918 D custom_postschedule
0x07fcb91c D custom_postschedule_async
0x07fcb920 D custom_presleepcheck
0x07fcb924 D custom_appsleepset
0x07fcb928 D custom_postsleepcheck
0x07fcb92c D custom_presleepenter
0x07fcb930 D custom_postsleepexit
0x07fcb934 D custom_prewakeup
0x07fcb938 D custom_postwakeup
0x07fcb93c D custom_preidlecheck
0x07fcb940 D custom_pti_set
0x07fcb944 D REG_BLE_EM_TX_BUFFER_SIZE
0x07fcb948 D REG_BLE_EM_RX_BUFFER_SIZE
0x07fcb94c D _ble_base
0x07fcb950 D gap_cfg_user
0x07fcb954 D rom_func_addr_table
0x07fcb958 D rom_cfg_table
0x07fcb95c D BLE_TX_DESC_DATA_USER
0x07fcb960 D BLE_TX_DESC_CNTL_USER
0x07fcb964 D LLM_LE_ADV_DUMMY_IDX
0x07fcb968 D LLM_LE_SCAN_CON_REQ_ADV_DIR_IDX
0x07fcb96c D LLM_LE_SCAN_RSP_IDX
0x07fcb970 D LLM_LE_ADV_IDX
0x07fcb974 D length_exchange_needed
0x07fcb978 D enh_con_cmp_cnt
0x07fcb980 D rx_pkt_cnt
0x07fcb984 D rx_pkt_cnt_bad
0x07fcb988 D rx_pkt_cnt_bad_adv
0x07fcb98c D rx_pkt_cnt_bad_scn
0x07fcb990 D rx_pkt_cnt_bad_oth
0x07fcb994 D rx_pkt_cnt_bad_wo_sync_err
0x07fcb998 D rx_pkt_cnt_bad_con
0x07fcb99c D connect_req_cnt
0x07fcb9a0 D last_status
0x07fcb9a4 D llc_state
0x07fcb9a8 D lld_sleep_env
0x07fcb9ac D lld_wlcoex_enable
0x07fcb9b0 D ble_duplicate_filter_max
0x07fcb9b1 D ble_duplicate_filter_found
0x07fcb9b4 D alter_conn_adv_all_cnt
0x07fcb9b8 D alter_conn_adv_dir_cnt
0x07fcb9bc D alter_conn_adv_cnt
0x07fcb9c0 D create_conn_cnt
0x07fcb9c4 D alter_conn_cnt
0x07fcb9c8 D alter_conn_restart_cnt
0x07fcb9cc D alter_conn_peer_addr
0x07fcb9d2 D alter_conn_local_addr
0x07fcb9d8 D set_adv_data_discard_old
0x07fcb9d9 D llm_resolving_list_max
0x07fcb9da D llm_local_le_feats
0x07fcb9e2 D llm_bt_env
0x07fcb9ec D init_tx_cnt_cntl_cnt1
0x07fcb9f0 D init_tx_cnt_cntl_cnt
0x07fcb9f4 D tx_cnt_cntl_cnt
0x07fcb9f8 D llm_state
0x07fcb9fa D delay_us_cnt
0x07fcb9fc D gtl_state
0x07fcb9fd D use_h4tl
0x07fcba00 D hci_cmd_desc_root_tab
0x07fcba30 D gattc_state
0x07fcba33 D gattm_state
0x07fcba34 D l2cc_state
0x07fcba38 D l2cm_env
0x07fcba3e D gapc_state
0x07fcba41 D gapm_state
0x07fcba42 D whitelist_fix
0x07fcba44 D ecdh_key_creation_in_progress
0x07fcba48 D ke_free_bad
0x07fcba4c D DISABLE_KE_TASK_ALTERNATIVE_SAVED_QUEUE
0x07fcba54 D rwip_env
0x07fcba5c D _rand_state_ROM_DATA
0x07fcba60 D custom_msg_handlers
0x07fcba64 D ble_reg_save
0x07fcbab4 D sleep_env
0x07fcbab8 D uart_env
0x07fcbadc D ke_mem_heaps_used
0x07fcbae0 D co_buf_env
0x07fcbb78 D llc_env
0x07fcbb84 D lld_evt_env
0x07fcbbb0 D llm_le_env
0x07fcbcb0 D llm_local_cmds
0x07fcbd30 D gtl_env
0x07fcbd78 D hci_env
0x07fcbd88 D h4tl_env
0x07fcbda0 D gattc_env
0x07fcbdac D gattm_env
0x07fcbdd0 D l2cc_env
0x07fcbddc D ecdh_key
0x07fcbe3c D gapc_env
0x07fcbe48 D gapm_env
0x07fcbe74 D ke_env
0x07fcbf58 D rwip_rf
