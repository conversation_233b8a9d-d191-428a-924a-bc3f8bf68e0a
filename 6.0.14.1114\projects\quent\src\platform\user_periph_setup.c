/**
 ****************************************************************************************
 *
 * @file user_periph_setup.c
 *
 * @brief Peripherals setup and initialization.
 *
 * Copyright (C) 2015-2019 Dialog Semiconductor.
 * This computer program includes Confidential, Proprietary Information
 * of Dialog Semiconductor. All Rights Reserved.
 *
 ****************************************************************************************
 */



/*
 * INCLUDE FILES
 ****************************************************************************************
 */
 #if (WLAN_COEX_ENABLED)
#include "wlan_coex.h"
#endif

#include "user_periph_setup.h"
#include "datasheet.h"
#include "system_library.h"
#include "rwip_config.h"
#include "gpio.h"
#include "uart.h"
#include "syscntl.h"

# include "time.h"
/*
 * GLOBAL VARIABLE DEFINITIONS
 ****************************************************************************************
 */

/**
 ****************************************************************************************
 * @brief Each application reserves its own GPIOs here.
 ****************************************************************************************
 */
#if (WLAN_COEX_ENABLED)
// Configuration struct for WLAN coexistence
const wlan_coex_cfg_t wlan_coex_cfg = {
 .ext_24g_eip_port = WLAN_COEX_24G_EIP_PORT,
 .ext_24g_eip_pin = WLAN_COEX_24G_EIP_PIN,
 .ble_eip_port = WLAN_COEX_BLE_EIP_PORT,
 .ble_eip_pin = WLAN_COEX_BLE_EIP_PIN,
   .ble_prio_port = WLAN_COEX_BLE_PRIO_PORT,
   .ble_prio_pin = WLAN_COEX_BLE_PRIO_PIN,
#if defined (CFG_WLAN_COEX_DEBUG)
   .debug_a_port = WLAN_COEX_DEBUG_A_PORT,
   .debug_a_pin = WLAN_COEX_DEBUG_A_PIN,
   .debug_b_port = WLAN_COEX_DEBUG_B_PORT,
   .debug_b_pin = WLAN_COEX_DEBUG_B_PIN,
#endif
   .irq = WLAN_COEX_IRQ,
};
#endif






#if DEVELOPMENT_DEBUG
	
void GPIO_reservations(void)
{
/*
    i.e. to reserve P0_1 as Generic Purpose I/O:
    RESERVE_GPIO(DESCRIPTIVE_NAME, GPIO_PORT_0, GPIO_PIN_1, PID_GPIO);
*/

#if defined (CFG_PRINTF_UART2)
    RESERVE_GPIO(UART2_TX, UART2_TX_PORT, UART2_TX_PIN, PID_UART2_TX);
#endif

    RESERVE_GPIO(LED, GPIO_LED_PORT, GPIO_LED_PIN, PID_GPIO);

#if !defined (__DA14586__)
    RESERVE_GPIO(SPI_EN, SPI_EN_PORT, SPI_EN_PIN, PID_SPI_EN);
#endif
}

void QUENT_GPIO_reservations()
{
  RESERVE_GPIO(UART1_TX, UART1_TX_PORT, UART1_TX_PIN, PID_UART1_TX);
	RESERVE_GPIO(UART1_RX, UART1_RX_PORT, UART1_RX_PIN, PID_UART1_RX);	
	RESERVE_GPIO(BLE_ST, GPIO_PORT_0, GPIO_PIN_3, PID_GPIO);
	RESERVE_GPIO(ST_BLE, GPIO_PORT_0, GPIO_PIN_4, PID_GPIO);
#ifndef EVK_BUILD
	RESERVE_GPIO(RF_SWITCH, GPIO_PORT_0, GPIO_PIN_5, PID_GPIO);
#endif
}

#endif


void set_pad_functions(void)
{
/*
    i.e. to set P0_1 as Generic purpose Output:
    GPIO_ConfigurePin(GPIO_PORT_0, GPIO_PIN_1, OUTPUT, PID_GPIO, false);
*/

#if defined (__DA14586__)
    // Disallow spontaneous DA14586 SPI Flash wake-up
    GPIO_ConfigurePin(GPIO_PORT_2, GPIO_PIN_3, OUTPUT, PID_GPIO, true);
#else
    // Disallow spontaneous SPI Flash wake-up
    GPIO_ConfigurePin(SPI_EN_PORT, SPI_EN_PIN, OUTPUT, PID_SPI_EN, true);
#endif

#if defined (CFG_PRINTF_UART2)
    // Configure UART2 TX Pad
    GPIO_ConfigurePin(UART2_TX_PORT, UART2_TX_PIN, OUTPUT, PID_UART2_TX, false);
#endif

    GPIO_ConfigurePin(GPIO_LED_PORT, GPIO_LED_PIN, OUTPUT, PID_GPIO, false);
}


void quent_set_pad_functions()
{
	
	GPIO_Disable_HW_Reset();
	
#ifndef EVK_BUILD
  SetBits16(SYS_CTRL_REG, DEBUGGER_ENABLE, 0);            // disabled JTAG for Wifi Co-ex								// Switch Antenna mux to BLE (watch)
#endif
	
#if (WLAN_COEX_ENABLED)
   wlan_coex_gpio_cfg();
#else
	 GPIO_ConfigurePin(GPIO_PORT_0, GPIO_PIN_5, OUTPUT, PID_GPIO, true); // TP 9 RF SWITCH
#endif

	 GPIO_ConfigurePin(GPIO_PORT_0, GPIO_PIN_4, INPUT_PULLDOWN, PID_GPIO, false); // ST_BLE
 	 GPIO_ConfigurePin(GPIO_PORT_0, GPIO_PIN_3, OUTPUT, PID_GPIO, false);// BLE_ST 
}



static uart_cfg_t uart_cfg = {
    .baud_rate = UART_BAUDRATE_230400,
    .data_bits = UART2_DATABITS,
    .parity = UART_PARITY_NONE,
    .stop_bits = UART2_STOPBITS,
    .auto_flow_control = UART2_AFCE,
    .use_fifo = UART2_FIFO,
    .tx_fifo_tr_lvl = UART2_TX_FIFO_LEVEL,
    .rx_fifo_tr_lvl = UART2_RX_FIFO_LEVEL,
    .intr_priority = 2,
};

void periph_init(void)
{
#if defined (__DA14531__)
    // In Boost mode enable the DCDC converter to supply VBAT_HIGH for the used GPIOs
    syscntl_dcdc_turn_on_in_boost(SYSCNTL_DCDC_LEVEL_3V0);
#else
    // Power up peripherals' power domain
    SetBits16(PMU_CTRL_REG, PERIPH_SLEEP, 0);
    while (!(GetWord16(SYS_STAT_REG) & PER_IS_UP));
    SetBits16(CLK_16M_REG, XTAL16_BIAS_SH_ENABLE, 1);
#endif

    // ROM patch
    patch_func();
	
#if (WLAN_COEX_ENABLED)
   RESERVE_GPIO(COEX_EIP, wlan_coex_cfg.ble_eip_port, wlan_coex_cfg.ble_eip_pin, PID_GPIO);
   RESERVE_GPIO(COEX_PRIO, wlan_coex_cfg.ble_prio_port, wlan_coex_cfg.ble_prio_pin, PID_GPIO);
   RESERVE_GPIO(COEX_REQ, wlan_coex_cfg.ext_24g_eip_port, wlan_coex_cfg.ext_24g_eip_pin, PID_GPIO);
#endif

#if defined (CFG_WLAN_COEX_DEBUG)
   RESERVE_GPIO(DEBUGPIN1, wlan_coex_cfg.debug_b_port, wlan_coex_cfg.debug_b_pin, PID_GPIO);
   RESERVE_GPIO(DEBUGPIN2, wlan_coex_cfg.debug_a_port, wlan_coex_cfg.debug_a_pin, PID_GPIO);
#endif

#if DEBUG_UART2
	   uart_initialize(UART2, &uart_cfg); //debug uart2
#endif		 

    // Set pad functionality
#ifndef QUENT		
    set_pad_functions();
#else
    quent_set_pad_functions();
#endif
    // Enable the pads
    GPIO_set_pad_latch_en(true);
		
}


void uart_reinit(void)
{
	
	Close_Uart(UART1);
	
	uart_disable(UART1);
	uart_cfg.baud_rate = UART_BAUDRATE_230400;
	uart_initialize(UART1, &uart_cfg);
	
	Open_Uart(UART1);
		
}

void uart_send_cb(uint16_t len)
{

}

void UART_OPEN_PORT_INIT(void)
{	
	  uart_initialize(UART1, &uart_cfg);
		uart_register_tx_cb(UART1, uart_send_cb);
//		uart_register_err_cb(UART1, uart_error_cb);
		Open_Uart(UART1);
	
	  GPIO_ConfigurePin(UART1_TX_PORT, UART1_TX_PIN, OUTPUT, PID_UART1_TX, false);
    GPIO_ConfigurePin(UART1_RX_PORT, UART1_RX_PIN, INPUT, PID_UART1_RX, false);
    GPIO_set_pad_latch_en(true);
	
}

void UART_CLOSE_PORT_INIT(void)
{
		Close_Uart(UART1);
		uart_disable(UART1);
	
}


void init_simple_ppg_uart(void)
{
    // Configure UART1 for PPG data reception
    static uart_cfg_t uart_cfg = {
        .baud_rate = UART_BAUDRATE_230400,
        .data_bits = UART_DATABITS_8,
        .parity = UART_PARITY_NONE,
        .stop_bits = UART_STOPBITS_1,
        .auto_flow_control = UART_AFCE_DIS,
        .use_fifo = UART_FIFO_EN,
        .tx_fifo_tr_lvl = UART_TX_FIFO_LEVEL_0,
        .rx_fifo_tr_lvl = UART_RX_FIFO_LEVEL_0,
        .intr_priority = 2,
    };
    
    // Initialize UART
    uart_initialize(UART1, &uart_cfg);
    
    // Enable RX interrupts
    uart_rxdata_intr_setf(UART1, UART_BIT_EN);
    
    // Register interrupt handler
    NVIC_SetPriority(UART_INTR(UART1), 2);
    NVIC_EnableIRQ(UART_INTR(UART1));
}

// UART1 interrupt service routine
// Simple PPG data handling - no communication manager
static uint8_t uart_rx_buffer[32];
static uint8_t rx_index = 0;

// UART interrupt handler for PPG data
void simple_uart_rx_handler(void)
{
    uint8_t received_byte;

    // Read one byte using the correct SDK function
    received_byte = uart_read_byte(UART1);

    // Simple state machine for packet detection
    static enum {
        WAIT_START1,
        WAIT_START2,
        COLLECT_DATA,
        PACKET_COMPLETE
    } rx_state = WAIT_START1;

    switch(rx_state) {
        case WAIT_START1:
            if(received_byte == 0xAA) {
                uart_rx_buffer[0] = received_byte;
                rx_index = 1;
                rx_state = WAIT_START2;
            }
            break;

        case WAIT_START2:
            if(received_byte == 0x55) {
                uart_rx_buffer[1] = received_byte;
                rx_index = 2;
                rx_state = COLLECT_DATA;
            } else {
                rx_state = WAIT_START1;
            }
            break;

        case COLLECT_DATA:
            uart_rx_buffer[rx_index++] = received_byte;
            if(rx_index >= 12) {
                rx_state = PACKET_COMPLETE;
            }
            break;

        case PACKET_COMPLETE:
            // Verify end marker and checksum
            if(uart_rx_buffer[11] == 0xFF) {
                // Verify checksum
                uint8_t checksum = 0;
                for(int i = 2; i < 10; i++) {
                    checksum ^= uart_rx_buffer[i];
                }

                if(checksum == uart_rx_buffer[10]) {
                    // Valid packet - send over BLE immediately
                    send_ppg_over_ble();
                }
            }
            rx_state = WAIT_START1;
            rx_index = 0;
            break;
    }
}

// Simple BLE transmission function (placeholder for now)
void send_ppg_over_ble(void)
{
    // TODO: Implement BLE transmission
    // For now, just extract the values for testing
    uint32_t red_value = uart_rx_buffer[2] |
                        (uart_rx_buffer[3] << 8) |
                        (uart_rx_buffer[4] << 16) |
                        (uart_rx_buffer[5] << 24);

    uint32_t ir_value = uart_rx_buffer[6] |
                       (uart_rx_buffer[7] << 8) |
                       (uart_rx_buffer[8] << 16) |
                       (uart_rx_buffer[9] << 24);

    // For now, just store the values (you can add BLE transmission later)
    // This prevents compilation errors while you test the UART reception
}



void UART1_Handler(void)
{
    // Check if data is available
    if(uart_data_ready_getf(UART1)) {
        simple_uart_rx_handler();
    }
}


