;#<FEEDBACK># ARM Linker, 5060061: Last Updated: Tue Sep 02 16:33:19 2025
;VERSION 0.2
;FILE adc_531.o
adc_correct_sample <= USED 0
adc_disable <= USED 0
adc_get_sample <= USED 0
adc_get_temp_async <= USED 0
adc_input_shift_config <= USED 0
adc_input_shift_disable <= USED 0
adc_input_shift_enable <= USED 0
adc_offset_calibrate <= USED 0
adc_register_interrupt <= USED 0
adc_reset <= USED 0
adc_unregister_interrupt <= USED 0
;FILE app.o
app_easy_gap_advertise_with_timeout_stop <= USED 0
app_easy_gap_dev_config_get_active <= USED 0
app_easy_gap_directed_advertise_get_active <= USED 0
app_easy_gap_directed_advertise_start <= USED 0
app_easy_gap_get_peer_features <= USED 0
app_easy_gap_non_connectable_advertise_get_active <= USED 0
app_easy_gap_non_connectable_advertise_start <= USED 0
app_easy_gap_param_update_get_active <= USED 0
app_easy_gap_start_connection_to <= USED 0
app_easy_gap_start_connection_to_get_active <= USED 0
app_easy_gap_start_connection_to_set <= USED 0
app_easy_gap_undirected_advertise_with_timeout_start <= USED 0
;FILE app_bond_db.o
default_app_bdb_get_device_info_from_slot <= USED 0
default_app_bdb_get_number_of_stored_irks <= USED 0
default_app_bdb_get_size <= USED 0
default_app_bdb_get_stored_irks <= USED 0
default_app_bdb_remove_entry <= USED 0
send_bond_data_to_host <= USED 0
;FILE app_customs.o
app_custs1_init <= USED 0
;FILE app_customs_common.o
app_custs1_val_wr_validate <= USED 0
;FILE app_customs_task.o
;FILE app_default_handlers.o
default_advertise_operation <= USED 0
default_app_generate_static_random_addr <= USED 0
default_app_on_disconnect <= USED 0
default_app_on_pairing_succeeded <= USED 0
default_app_on_tk_exch <= USED 0
;FILE app_diss.o
;FILE app_diss_task.o
;FILE app_easy_msg_utils.o
app_easy_msg_free_callback <= USED 0
app_easy_msg_modify <= USED 0
app_easy_msg_set <= USED 0
app_easy_wakeup <= USED 0
app_easy_wakeup_free <= USED 0
app_easy_wakeup_set <= USED 0
app_msg_utils_api_process_handler <= USED 0
;FILE app_easy_security.o
app_easy_security_bdb_get_device_info_from_slot <= USED 0
app_easy_security_bdb_get_number_of_stored_irks <= USED 0
app_easy_security_bdb_get_size <= USED 0
app_easy_security_bdb_get_stored_irks <= USED 0
app_easy_security_bdb_remove_entry <= USED 0
app_easy_security_csrk_get_active <= USED 0
app_easy_security_encrypt_cfm_get_active <= USED 0
app_easy_security_ltk_exch_get_active <= USED 0
app_easy_security_pairing_rsp_get_active <= USED 0
app_easy_security_ral_op <= USED 0
app_easy_security_ral_sync_with_bdb <= USED 0
app_easy_security_request <= USED 0
app_easy_security_request_get_active <= USED 0
app_easy_security_resolve_bdaddr <= USED 0
app_easy_security_set_ltk_exch <= USED 0
app_easy_security_tk_get_active <= USED 0
;FILE app_easy_timer.o
app_easy_timer_cancel_all <= USED 0
;FILE app_easy_whitelist.o
app_easy_manage_wlist <= USED 0
;FILE app_msg_utils.o
app_msg_send_wakeup_ble <= USED 0
;FILE app_security.o
;FILE app_security_task.o
;FILE app_task.o
;FILE app_utils.o
app_get_address_type <= USED 0
;FILE arch_hibernation.o
arch_hibernation_restore <= USED 0
arch_set_hibernation <= USED 0
arch_set_stateful_hibernation <= USED 0
;FILE arch_main.o
;FILE arch_rom.o
;FILE arch_sleep.o
arch_ble_ext_wakeup_off <= USED 0
arch_ble_ext_wakeup_on <= USED 0
arch_force_active_mode <= USED 0
arch_last_rwble_evt_get <= USED 0
arch_restore_sleep_mode <= USED 0
arch_set_deep_sleep <= USED 0
;FILE arch_system.o
arch_set_pxact_gpio <= USED 0
arch_wkupct_tweak_deb_time <= USED 0
check_sys_startup_period <= USED 0
;FILE ble_arp.o
;FILE comm_manager.o
Bufferxfer_Status <= USED 0
Com_BufferXfer <= USED 0
Command_Status <= USED 0
Indication_Status <= USED 0
Response_Status <= USED 0
print_comm_queue <= USED 0
resetCommManager <= USED 0
;FILE comm_task.o
Comm_Sleep_Clear <= USED 0
Comm_Sleep_Clear_isr <= USED 0
Handle_Sleep <= USED 0
;FILE gpio.o
GPIO_ConfigurePinPower <= USED 0
GPIO_DisablePorPin <= USED 0
GPIO_EnablePorPin <= USED 0
GPIO_GetIRQInputLevel <= USED 0
GPIO_GetPinFunction <= USED 0
GPIO_GetPorTime <= USED 0
GPIO_SetIRQInputLevel <= USED 0
GPIO_SetPorTime <= USED 0
GPIO_is_valid <= USED 0
;FILE hardfault_handler.o
;FILE hash.o
;FILE hw_otpc_531.o
hw_otpc_prog <= USED 0
hw_otpc_prog_and_verify <= USED 0
hw_otpc_read <= USED 0
hw_otpc_word_prog_and_verify <= USED 0
hw_otpc_word_read <= USED 0
;FILE jump_table.o
dummyf <= USED 0
;FILE nmi_handler.o
;FILE otp_cs.o
otp_cs_get_adc_offsh_ge <= USED 0
otp_cs_get_adc_offsh_offset <= USED 0
otp_cs_get_low_power_clock <= USED 0
;FILE otp_hdr.o
;FILE patch.o
;FILE prf.o
;FILE primitivemanager.o
isPrimitiveIdle <= USED 0
timeoutPrimitiveManager <= USED 0
;FILE primitivequeue.o
;FILE rf_531.o
dcoff_calibration <= USED 0
dis_kdco_cal <= USED 0
en_hclk <= USED 0
en_kdtc_cal_mod1 <= USED 0
kdco_cal_end <= USED 0
kdco_cal_init <= USED 0
kdco_calibration <= USED 0
rf_adplldig_ldo_on <= USED 0
rf_adplldig_txmod <= USED 0
rf_ldo_cont_mode_en <= USED 0
rf_nfm_disable <= USED 0
rf_nfm_enable <= USED 0
rf_power_down <= USED 0
;FILE ring_buf.o
RingBuf_DeInit <= USED 0
RingBuf_num_free <= USED 0
RingBuf_peek <= USED 0
RingBuf_process_all <= USED 0
;FILE rwble.o
;FILE rwip.o
;FILE scheduler.o
InitialiseScheduler <= USED 0
ModifySchTime <= USED 0
Stop_Scheduler <= USED 0
;FILE serialinterface.o
Close_Rbuffer <= USED 0
Open_Rbuffer <= USED 0
port_open <= USED 0
;FILE startup_da14531.o
;FILE syscntl.o
syscntl_dcdc_turn_off <= USED 0
syscntl_dcdc_turn_on_in_buck <= USED 0
syscntl_por_vbat_high_cfg <= USED 0
syscntl_por_vbat_low_cfg <= USED 0
;FILE system_da14531.o
SystemCoreClockUpdate <= USED 0
;FILE transportmanager.o
isComClose <= USED 0
;FILE transportqueue.o
;FILE trng.o
;FILE uart.o
Close_Uart <= USED 0
FLUSH_RING_BUFFER <= USED 0
Open_Uart <= USED 0
ReadUart <= USED 0
UART_PEEK <= USED 0
get_tot_size <= USED 0
reset_ring_buffer <= USED 0
uart_disable_flow_control <= USED 0
uart_enable_flow_control <= USED 0
uart_read_buffer <= USED 0
uart_read_byte <= USED 0
uart_receive <= USED 0
uart_register_err_cb <= USED 0
uart_register_rx_cb <= USED 0
uart_register_tx_cb <= USED 0
uart_wait_tx_finish <= USED 0
;FILE upperlayerinterface.o
;FILE user_custs1_impl.o
Get_Indication_src_id <= USED 0
ResetCommLock <= USED 0
ResetCommLock_cb <= USED 0
Send_ECG <= USED 0
Send_NTF_User_Info_Data <= USED 0
Send_PPG <= USED 0
StartMeasuremet_Wait_Timer <= USED 0
clear_conn_status <= USED 0
;FILE user_periph_setup.o
UART1_Handler <= USED 0
UART_CLOSE_PORT_INIT <= USED 0
UART_OPEN_PORT_INIT <= USED 0
send_ppg_over_ble <= USED 0
set_pad_functions <= USED 0
simple_uart_rx_handler <= USED 0
uart_reinit <= USED 0
uart_send_cb <= USED 0
;FILE user_peripheral.o
handleUserConfigAck <= USED 0
hdlVitalBusy <= USED 0
